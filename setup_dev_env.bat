@echo off
echo 正在设置开发环境变量...

REM 添加 Rust 到 PATH
setx PATH "%PATH%;%USERPROFILE%\.cargo\bin" /M 2>nul || setx PATH "%PATH%;%USERPROFILE%\.cargo\bin"

REM 添加 Java 到 PATH
setx PATH "%PATH%;%USERPROFILE%\Java\jdk-21.0.2\bin" /M 2>nul || setx PATH "%PATH%;%USERPROFILE%\Java\jdk-21.0.2\bin"
setx JAVA_HOME "%USERPROFILE%\Java\jdk-21.0.2" /M 2>nul || setx JAVA_HOME "%USERPROFILE%\Java\jdk-21.0.2"

REM 添加 Go 到 PATH (如果安装成功)
if exist "C:\Program Files\Go\bin\go.exe" (
    setx PATH "%PATH%;C:\Program Files\Go\bin" /M 2>nul || setx PATH "%PATH%;C:\Program Files\Go\bin"
    setx GOROOT "C:\Program Files\Go" /M 2>nul || setx GOROOT "C:\Program Files\Go"
)

echo 环境变量设置完成！
echo 请重新启动命令提示符或 PowerShell 以使更改生效。
pause
