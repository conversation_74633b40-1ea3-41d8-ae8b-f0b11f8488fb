#!/usr/bin/env python3
"""
简单的真实AI测试 - 直接使用Hugging Face模型
"""

import sys
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

print("🚀 真实本地AI模型测试")
print("=" * 50)

# 检查系统
print(f"Python: {sys.version.split()[0]}")
print(f"PyTorch: {torch.__version__}")
print(f"CUDA: {torch.cuda.is_available()}")
print()

# 加载真实模型
print("📦 加载真实AI模型...")
model_name = "microsoft/DialoGPT-small"

try:
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(model_name)
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print("✅ 真实AI模型加载成功！")
    print(f"模型: {model_name}")
    print(f"参数量: ~117M")
    print()
    
    # 交互式对话
    print("🤖 开始与真实AI对话 (输入 'quit' 退出):")
    print("-" * 50)
    
    while True:
        user_input = input("\n您: ").strip()
        
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
        
        if not user_input:
            continue
        
        try:
            # 编码输入
            inputs = tokenizer.encode(user_input + tokenizer.eos_token, return_tensors="pt")
            
            # 生成响应
            print("🤔 AI正在思考...")
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 50,
                    temperature=0.8,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    no_repeat_ngram_size=2
                )
            
            # 解码响应
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            ai_response = response[len(user_input):].strip()
            
            if ai_response:
                print(f"🤖 AI: {ai_response}")
            else:
                print("🤖 AI: [思考中...]")
                
        except Exception as e:
            print(f"❌ 生成错误: {e}")

except Exception as e:
    print(f"❌ 模型加载失败: {e}")
    print("\n可能的解决方案:")
    print("1. 确保网络连接正常")
    print("2. 运行 python quick_model_test.py 先下载模型")
    print("3. 检查磁盘空间是否足够")

print("\n按任意键退出...")
input()
