
# 🤖 已下载的AI模型信息

## 模型详情
- **名称**: microsoft/DialoGPT-small
- **类型**: 对话生成模型
- **大小**: ~300MB
- **来源**: Microsoft/Hugging Face
- **功能**: 英文对话生成

## 使用方法
```python
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

# 加载模型
tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-small")
model = AutoModelForCausalLM.from_pretrained("microsoft/DialoGPT-small")

# 生成对话
def chat(message):
    inputs = tokenizer.encode(message + tokenizer.eos_token, return_tensors="pt")
    with torch.no_grad():
        outputs = model.generate(inputs, max_length=50, temperature=0.7, do_sample=True)
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response[len(message):].strip()

# 使用示例
print(chat("Hello, how are you?"))
```

## 模型位置
模型文件已缓存到: `~/.cache/huggingface/transformers/`

## 下一步
1. 可以下载更大更强的模型
2. 集成到AI编辑器中
3. 添加更多功能
