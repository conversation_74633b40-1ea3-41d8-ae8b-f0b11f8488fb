#!/usr/bin/env python3
"""
简化版本地AI编辑器 - 可以直接运行
"""

import os
import sys
import json
import base64
import logging
from datetime import datetime
from pathlib import Path
import webbrowser
import threading
import time

# 尝试导入必要的库，如果没有就提示安装
try:
    from fastapi import FastAPI, HTTPException
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import HTMLResponse
    from pydantic import BaseModel
    import uvicorn
except ImportError as e:
    print(f"缺少依赖库: {e}")
    print("正在安装必要的依赖...")
    os.system(f"{sys.executable} -m pip install fastapi uvicorn pydantic")
    from fastapi import FastAPI, HTTPException
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import HTMLResponse
    from pydantic import BaseModel
    import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="本地AI编辑器", description="简化版本地AI编辑器")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class CodeRequest(BaseModel):
    prompt: str
    language: str = "python"

class ChatRequest(BaseModel):
    message: str

class ImageRequest(BaseModel):
    prompt: str
    width: int = 512
    height: int = 512

# 模拟AI响应（在没有真实模型时使用）
def simulate_code_generation(prompt, language):
    """模拟代码生成"""
    if "hello world" in prompt.lower():
        if language == "python":
            return 'print("Hello, World!")'
        elif language == "javascript":
            return 'console.log("Hello, World!");'
        elif language == "java":
            return '''public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}'''
    
    return f"""# {language.upper()} 代码示例
# 根据提示: {prompt}

def example_function():
    '''
    这是一个示例函数
    实际的AI模型会生成更智能的代码
    '''
    print("AI编辑器正在工作...")
    return "success"

# 调用函数
example_function()
"""

def simulate_chat_response(message):
    """模拟聊天响应"""
    responses = {
        "你好": "您好！我是本地AI助手，很高兴为您服务！",
        "hello": "Hello! I'm your local AI assistant. How can I help you today?",
        "帮助": "我可以帮您生成代码、回答问题、创建图像等。请告诉我您需要什么帮助。",
        "代码": "我可以帮您生成各种编程语言的代码，包括Python、JavaScript、Java等。",
        "图像": "图像生成功能正在开发中，敬请期待！",
    }
    
    for key, response in responses.items():
        if key in message:
            return response
    
    return f"我理解您说的是：'{message}'。作为本地AI助手，我正在学习如何更好地回应您。目前我可以帮您生成代码、回答简单问题。请尝试说'帮助'了解更多功能。"

# API端点
@app.get("/")
async def root():
    return {"message": "本地AI编辑器运行中", "status": "ready"}

@app.get("/app", response_class=HTMLResponse)
async def get_app():
    """返回内嵌的前端应用"""
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地AI编辑器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; color: white; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .tabs { display: flex; background: rgba(255,255,255,0.1); border-radius: 10px; padding: 5px; margin-bottom: 20px; }
        .tab { flex: 1; padding: 15px; text-align: center; background: transparent; border: none; color: white; cursor: pointer; border-radius: 8px; }
        .tab.active { background: rgba(255,255,255,0.2); }
        .tab-content { display: none; background: rgba(255,255,255,0.95); border-radius: 15px; padding: 30px; }
        .tab-content.active { display: block; }
        .input-group { margin-bottom: 20px; }
        .input-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .input-group input, .input-group textarea, .input-group select { 
            width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 16px; 
        }
        .input-group textarea { min-height: 120px; resize: vertical; }
        .btn { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; 
            padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 16px; margin-right: 10px; 
        }
        .btn:hover { transform: translateY(-2px); }
        .output { 
            margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px; 
            border-left: 4px solid #667eea; min-height: 100px; 
        }
        .code-output { background: #2d3748; color: #e2e8f0; font-family: 'Courier New', monospace; white-space: pre-wrap; }
        .chat-container { height: 400px; overflow-y: auto; border: 2px solid #e1e5e9; border-radius: 8px; padding: 15px; margin-bottom: 15px; background: #f8f9fa; }
        .chat-message { margin-bottom: 15px; padding: 10px; border-radius: 8px; }
        .chat-message.user { background: #667eea; color: white; margin-left: 20%; }
        .chat-message.assistant { background: #e2e8f0; color: #2d3748; margin-right: 20%; }
        .status { position: fixed; bottom: 20px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 10px 15px; border-radius: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 本地AI编辑器</h1>
            <p>代码生成 • 智能对话 • 本地运行</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="switchTab('code')">💻 代码生成</button>
            <button class="tab" onclick="switchTab('chat')">💬 智能对话</button>
            <button class="tab" onclick="switchTab('image')">🎨 图像生成</button>
        </div>

        <!-- 代码生成 -->
        <div id="code" class="tab-content active">
            <h2>💻 代码生成器</h2>
            <div class="input-group">
                <label>编程语言</label>
                <select id="codeLanguage">
                    <option value="python">Python</option>
                    <option value="javascript">JavaScript</option>
                    <option value="java">Java</option>
                    <option value="cpp">C++</option>
                </select>
            </div>
            <div class="input-group">
                <label>代码需求描述</label>
                <textarea id="codePrompt" placeholder="请描述您需要的代码功能，例如：创建一个Hello World程序"></textarea>
            </div>
            <button class="btn" onclick="generateCode()">生成代码</button>
            <div class="output code-output" id="codeOutput">生成的代码将显示在这里...</div>
        </div>

        <!-- 智能对话 -->
        <div id="chat" class="tab-content">
            <h2>💬 智能对话助手</h2>
            <div class="chat-container" id="chatContainer">
                <div class="chat-message assistant">
                    <strong>AI助手:</strong> 您好！我是您的本地AI助手。我可以帮您生成代码、回答问题。请问有什么可以帮您的吗？
                </div>
            </div>
            <div class="input-group">
                <input type="text" id="chatInput" placeholder="请输入您的问题..." onkeypress="if(event.key==='Enter') sendMessage()">
            </div>
            <button class="btn" onclick="sendMessage()">发送消息</button>
        </div>

        <!-- 图像生成 -->
        <div id="image" class="tab-content">
            <h2>🎨 图像生成器</h2>
            <div class="input-group">
                <label>图像描述</label>
                <textarea id="imagePrompt" placeholder="请描述您想要生成的图像"></textarea>
            </div>
            <button class="btn" onclick="generateImage()">生成图像</button>
            <div class="output" id="imageOutput">图像生成功能正在开发中...</div>
        </div>
    </div>

    <div class="status" id="status">系统就绪</div>

    <script>
        // 切换标签页
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 代码生成
        async function generateCode() {
            const language = document.getElementById('codeLanguage').value;
            const prompt = document.getElementById('codePrompt').value;
            
            if (!prompt.trim()) {
                alert('请输入代码需求描述');
                return;
            }

            document.getElementById('status').textContent = '正在生成代码...';

            try {
                const response = await fetch('/api/generate_code', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ prompt: prompt, language: language })
                });

                const data = await response.json();
                document.getElementById('codeOutput').textContent = data.code;
                document.getElementById('status').textContent = '代码生成完成';
            } catch (error) {
                document.getElementById('codeOutput').textContent = '生成失败: ' + error.message;
                document.getElementById('status').textContent = '生成失败';
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message) return;

            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML += `<div class="chat-message user"><strong>您:</strong> ${message}</div>`;
            input.value = '';
            chatContainer.scrollTop = chatContainer.scrollHeight;

            document.getElementById('status').textContent = 'AI正在思考...';

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();
                chatContainer.innerHTML += `<div class="chat-message assistant"><strong>AI助手:</strong> ${data.response}</div>`;
                chatContainer.scrollTop = chatContainer.scrollHeight;
                document.getElementById('status').textContent = '对话完成';
            } catch (error) {
                chatContainer.innerHTML += `<div class="chat-message assistant"><strong>AI助手:</strong> 抱歉，发生了错误: ${error.message}</div>`;
                document.getElementById('status').textContent = '对话失败';
            }
        }

        // 图像生成
        function generateImage() {
            alert('图像生成功能正在开发中，敬请期待！');
        }

        // 检查后端状态
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/');
                const data = await response.json();
                document.getElementById('status').textContent = data.message;
            } catch (error) {
                document.getElementById('status').textContent = '后端连接失败';
            }
        });
    </script>
</body>
</html>
    """
    return html_content

@app.post("/api/generate_code")
async def generate_code(request: CodeRequest):
    """生成代码"""
    try:
        code = simulate_code_generation(request.prompt, request.language)
        return {
            "code": code,
            "language": request.language,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/chat")
async def chat(request: ChatRequest):
    """对话功能"""
    try:
        response = simulate_chat_response(request.message)
        return {
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open("http://localhost:8000/app")

def main():
    """主函数"""
    print("🚀 启动本地AI编辑器...")
    print("=" * 50)
    print(f"Python路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    print("=" * 50)
    
    # 在后台线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("🌐 启动Web服务器...")
    print("📱 浏览器将自动打开: http://localhost:8000/app")
    print("🔗 API文档: http://localhost:8000/docs")
    print("\n按 Ctrl+C 停止服务")
    
    try:
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
    except KeyboardInterrupt:
        print("\n👋 服务已停止")

if __name__ == "__main__":
    main()
