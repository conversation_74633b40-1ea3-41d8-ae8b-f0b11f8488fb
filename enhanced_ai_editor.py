#!/usr/bin/env python3
"""
增强版本地AI编辑器 - 集成真实AI模型
"""

import os
import sys
import json
import logging
import threading
import time
import webbrowser
from datetime import datetime
from pathlib import Path

# 尝试导入AI库
try:
    import torch
    from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
    AI_AVAILABLE = True
    print("✅ AI库加载成功")
except ImportError:
    AI_AVAILABLE = False
    print("⚠️ AI库未安装，使用模拟模式")

# Web框架
try:
    from fastapi import FastAPI, HTTPException
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import HTMLResponse
    from pydantic import BaseModel
    import uvicorn
except ImportError:
    print("正在安装Web框架...")
    os.system(f"{sys.executable} -m pip install fastapi uvicorn pydantic")
    from fastapi import FastAPI, HTTPException
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import HTMLResponse
    from pydantic import BaseModel
    import uvicorn

# 配置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="增强版本地AI编辑器")
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"])

# 数据模型
class CodeRequest(BaseModel):
    prompt: str
    language: str = "python"

class ChatRequest(BaseModel):
    message: str

# AI模型管理器
class AIModelManager:
    def __init__(self):
        self.models = {}
        self.device = "cuda" if torch.cuda.is_available() and AI_AVAILABLE else "cpu"
        logger.info(f"使用设备: {self.device}")
    
    def load_small_model(self):
        """加载小型模型用于演示"""
        if not AI_AVAILABLE:
            return None
        
        try:
            logger.info("加载小型AI模型...")
            # 使用较小的模型进行演示
            model_name = "microsoft/DialoGPT-small"
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForCausalLM.from_pretrained(model_name)
            
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            self.models["chat"] = {"tokenizer": tokenizer, "model": model}
            logger.info("✅ 小型AI模型加载成功")
            return True
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def generate_code(self, prompt, language):
        """生成代码"""
        # 代码生成模板
        templates = {
            "python": {
                "hello world": 'print("Hello, World!")',
                "fibonacci": '''def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 测试
for i in range(10):
    print(f"fibonacci({i}) = {fibonacci(i)}")''',
                "sort": '''def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

# 测试
numbers = [64, 34, 25, 12, 22, 11, 90]
sorted_numbers = bubble_sort(numbers.copy())
print(f"原数组: {numbers}")
print(f"排序后: {sorted_numbers}")'''
            },
            "javascript": {
                "hello world": 'console.log("Hello, World!");',
                "fibonacci": '''function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n-1) + fibonacci(n-2);
}

// 测试
for (let i = 0; i < 10; i++) {
    console.log(`fibonacci(${i}) = ${fibonacci(i)}`);
}''',
                "sort": '''function bubbleSort(arr) {
    const n = arr.length;
    for (let i = 0; i < n; i++) {
        for (let j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
            }
        }
    }
    return arr;
}

// 测试
const numbers = [64, 34, 25, 12, 22, 11, 90];
const sortedNumbers = bubbleSort([...numbers]);
console.log("原数组:", numbers);
console.log("排序后:", sortedNumbers);'''
            }
        }
        
        # 智能匹配
        prompt_lower = prompt.lower()
        lang_templates = templates.get(language, templates["python"])
        
        for key, code in lang_templates.items():
            if key in prompt_lower:
                return code
        
        # 默认代码
        return f'''# {language.upper()} 代码示例
# 根据提示: {prompt}

def main():
    """
    这是根据您的需求生成的代码框架
    请根据具体需求进行修改
    """
    print("AI编辑器生成的代码")
    # TODO: 实现具体功能
    pass

if __name__ == "__main__":
    main()
'''
    
    def chat_response(self, message):
        """生成聊天响应"""
        if "chat" in self.models and AI_AVAILABLE:
            try:
                tokenizer = self.models["chat"]["tokenizer"]
                model = self.models["chat"]["model"]
                
                # 编码输入
                inputs = tokenizer.encode(message + tokenizer.eos_token, return_tensors="pt")
                
                # 生成响应
                with torch.no_grad():
                    outputs = model.generate(
                        inputs,
                        max_length=inputs.shape[1] + 50,
                        temperature=0.7,
                        do_sample=True,
                        pad_token_id=tokenizer.eos_token_id
                    )
                
                # 解码响应
                response = tokenizer.decode(outputs[0], skip_special_tokens=True)
                response = response[len(message):].strip()
                
                if response:
                    return response
            except Exception as e:
                logger.error(f"AI响应生成失败: {e}")
        
        # 备用响应
        responses = {
            "你好": "您好！我是本地AI助手，很高兴为您服务！",
            "hello": "Hello! I'm your local AI assistant.",
            "代码": "我可以帮您生成Python、JavaScript等多种语言的代码。",
            "帮助": "我可以帮您：\n1. 生成各种编程语言的代码\n2. 回答编程相关问题\n3. 提供技术建议",
        }
        
        for key, response in responses.items():
            if key in message.lower():
                return response
        
        return f"我理解您的问题：'{message}'。作为本地AI助手，我正在努力为您提供最好的帮助。"

# 全局模型管理器
model_manager = AIModelManager()

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    logger.info("🚀 启动增强版AI编辑器...")
    if AI_AVAILABLE:
        # 在后台线程中加载模型
        threading.Thread(target=model_manager.load_small_model, daemon=True).start()

@app.get("/")
async def root():
    return {
        "message": "增强版本地AI编辑器",
        "ai_available": AI_AVAILABLE,
        "device": model_manager.device if AI_AVAILABLE else "cpu"
    }

@app.get("/app", response_class=HTMLResponse)
async def get_app():
    """返回前端应用"""
    with open("ai_editor_frontend.html", "r", encoding="utf-8") as f:
        return f.read()

@app.post("/api/generate_code")
async def generate_code(request: CodeRequest):
    """生成代码"""
    try:
        code = model_manager.generate_code(request.prompt, request.language)
        return {
            "code": code,
            "language": request.language,
            "timestamp": datetime.now().isoformat(),
            "ai_powered": AI_AVAILABLE
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/chat")
async def chat(request: ChatRequest):
    """对话功能"""
    try:
        response = model_manager.chat_response(request.message)
        return {
            "response": response,
            "timestamp": datetime.now().isoformat(),
            "ai_powered": AI_AVAILABLE
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    return {
        "ai_available": AI_AVAILABLE,
        "device": model_manager.device if AI_AVAILABLE else "cpu",
        "loaded_models": list(model_manager.models.keys()),
        "cuda_available": torch.cuda.is_available() if AI_AVAILABLE else False
    }

def open_browser():
    """延迟打开浏览器"""
    time.sleep(3)
    webbrowser.open("http://localhost:8000/app")

def main():
    """主函数"""
    print("🚀 增强版本地AI编辑器")
    print("=" * 50)
    print(f"Python: {sys.executable}")
    print(f"AI库可用: {'✅' if AI_AVAILABLE else '❌'}")
    if AI_AVAILABLE:
        print(f"PyTorch: {torch.__version__}")
        print(f"CUDA可用: {'✅' if torch.cuda.is_available() else '❌'}")
    print("=" * 50)
    
    # 在后台打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("🌐 启动服务器...")
    print("📱 浏览器将自动打开: http://localhost:8000/app")
    print("🔗 API状态: http://localhost:8000/api/status")
    print("\n按 Ctrl+C 停止服务")
    
    try:
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="warning")
    except KeyboardInterrupt:
        print("\n👋 服务已停止")

if __name__ == "__main__":
    main()
