<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地AI编辑器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333; padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; color: white; }
        .header h1 { font-size: 3em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .card { background: white; border-radius: 15px; padding: 30px; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .input-group { margin-bottom: 20px; }
        .input-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #555; }
        .input-group input, .input-group textarea, .input-group select {
            width: 100%; padding: 15px; border: 2px solid #e1e5e9; border-radius: 10px; font-size: 16px;
            transition: all 0.3s ease;
        }
        .input-group input:focus, .input-group textarea:focus, .input-group select:focus {
            outline: none; border-color: #667eea; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .input-group textarea { min-height: 120px; resize: vertical; font-family: inherit; }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;
            padding: 15px 30px; border-radius: 10px; cursor: pointer; font-size: 16px; margin-right: 10px;
            transition: all 0.3s ease; font-weight: 600;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.2); }
        .btn:active { transform: translateY(0); }
        .output {
            margin-top: 20px; padding: 25px; background: #f8f9fa; border-radius: 10px;
            border-left: 5px solid #667eea; min-height: 150px;
        }
        .code-output {
            background: #2d3748; color: #e2e8f0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            white-space: pre-wrap; line-height: 1.5; font-size: 14px;
        }
        .tabs {
            display: flex; margin-bottom: 20px; background: rgba(255,255,255,0.1);
            border-radius: 15px; padding: 8px; backdrop-filter: blur(10px);
        }
        .tab {
            flex: 1; padding: 15px; text-align: center; background: transparent; border: none;
            color: white; cursor: pointer; border-radius: 10px; transition: all 0.3s ease;
            font-size: 16px; font-weight: 600;
        }
        .tab.active { background: rgba(255,255,255,0.2); transform: translateY(-2px); }
        .tab:hover { background: rgba(255,255,255,0.1); }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .status {
            position: fixed; bottom: 20px; right: 20px; background: rgba(0,0,0,0.8);
            color: white; padding: 12px 20px; border-radius: 25px; font-size: 14px;
            backdrop-filter: blur(10px);
        }
        .chat-message { margin-bottom: 15px; padding: 15px; border-radius: 10px; }
        .chat-message.user { background: #667eea; color: white; margin-left: 20%; }
        .chat-message.assistant { background: #e2e8f0; color: #2d3748; margin-right: 20%; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .feature-card { background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #667eea; }
        .feature-card h3 { color: #667eea; margin-bottom: 10px; }
        .loading { display: none; text-align: center; padding: 20px; }
        .loading.show { display: block; }
        .spinner {
            border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%;
            width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 10px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .example-buttons { margin: 15px 0; }
        .example-btn {
            background: #e3f2fd; color: #1976d2; border: 1px solid #bbdefb;
            padding: 8px 15px; border-radius: 20px; cursor: pointer; margin: 5px;
            font-size: 14px; transition: all 0.3s ease;
        }
        .example-btn:hover { background: #bbdefb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 本地AI编辑器</h1>
            <p>智能代码生成 • 对话助手 • 完全本地运行 • 无需联网 • 保护隐私</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="switchTab('code')">💻 代码生成</button>
            <button class="tab" onclick="switchTab('chat')">💬 智能对话</button>
            <button class="tab" onclick="switchTab('features')">✨ 功能特性</button>
            <button class="tab" onclick="switchTab('about')">ℹ️ 关于</button>
        </div>

        <!-- 代码生成 -->
        <div id="code" class="tab-content active">
            <div class="card">
                <h2>💻 智能代码生成器</h2>
                <p style="color: #666; margin-bottom: 20px;">描述您需要的代码功能，AI将为您生成高质量的代码</p>

                <div class="input-group">
                    <label>🔧 编程语言</label>
                    <select id="codeLanguage">
                        <option value="python">🐍 Python</option>
                        <option value="javascript">🟨 JavaScript</option>
                        <option value="java">☕ Java</option>
                        <option value="cpp">⚡ C++</option>
                        <option value="rust">🦀 Rust</option>
                        <option value="go">🐹 Go</option>
                        <option value="csharp">💙 C#</option>
                    </select>
                </div>

                <div class="input-group">
                    <label>📝 代码需求描述</label>
                    <textarea id="codePrompt" placeholder="请详细描述您需要的代码功能，例如：
• 创建一个Hello World程序
• 实现斐波那契数列算法
• 编写冒泡排序函数
• 创建一个简单的计算器
• 实现二分查找算法
• 创建一个文件读写程序"></textarea>
                </div>

                <div class="example-buttons">
                    <span style="color: #666; font-weight: 600;">💡 快速示例：</span>
                    <button class="example-btn" onclick="setExample('hello world')">Hello World</button>
                    <button class="example-btn" onclick="setExample('fibonacci')">斐波那契数列</button>
                    <button class="example-btn" onclick="setExample('sort')">排序算法</button>
                    <button class="example-btn" onclick="setExample('calculator')">计算器</button>
                    <button class="example-btn" onclick="setExample('file')">文件操作</button>
                </div>

                <button class="btn" onclick="generateCode()">🎯 生成代码</button>
                <button class="btn" onclick="copyCode()" style="background: #28a745;">📋 复制代码</button>

                <div class="loading" id="codeLoading">
                    <div class="spinner"></div>
                    <p>AI正在为您生成代码...</p>
                </div>

                <div class="output code-output" id="codeOutput">生成的代码将显示在这里...

🎯 使用提示：
• 详细描述您的需求，AI会生成更准确的代码
• 支持多种编程语言
• 生成的代码可以直接复制使用
• 尝试点击上方的快速示例按钮</div>
            </div>
        </div>

        <!-- 智能对话 -->
        <div id="chat" class="tab-content">
            <div class="card">
                <h2>💬 智能对话助手</h2>
                <p style="color: #666; margin-bottom: 20px;">与AI助手对话，获取编程建议和技术支持</p>

                <div class="output" id="chatOutput" style="height: 400px; overflow-y: auto; background: white; border: 2px solid #e1e5e9;">
                    <div class="chat-message assistant">
                        <strong>🤖 AI助手:</strong> 您好！我是您的本地AI编程助手。我可以帮您：<br>
                        • 🔧 生成各种编程语言的代码<br>
                        • 💡 解答编程相关问题<br>
                        • 🚀 提供技术建议和最佳实践<br>
                        • 🐛 协助调试和优化代码<br><br>
                        请问有什么可以帮您的吗？
                    </div>
                </div>

                <div class="input-group">
                    <input type="text" id="chatInput" placeholder="请输入您的问题，例如：如何学习Python？什么是递归？如何优化代码性能？" onkeypress="if(event.key==='Enter') sendMessage()">
                </div>

                <div class="example-buttons">
                    <span style="color: #666; font-weight: 600;">💡 常见问题：</span>
                    <button class="example-btn" onclick="askQuestion('如何学习编程？')">学习编程</button>
                    <button class="example-btn" onclick="askQuestion('什么是算法？')">算法概念</button>
                    <button class="example-btn" onclick="askQuestion('如何选择编程语言？')">语言选择</button>
                    <button class="example-btn" onclick="askQuestion('代码优化技巧')">优化技巧</button>
                </div>

                <button class="btn" onclick="sendMessage()">📤 发送消息</button>
                <button class="btn" onclick="clearChat()" style="background: #dc3545;">🗑️ 清空对话</button>
            </div>
        </div>

        <!-- 功能特性 -->
        <div id="features" class="tab-content">
            <div class="card">
                <h2>✨ 功能特性</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🎯 智能代码生成</h3>
                        <p>支持Python、JavaScript、Java、C++、Rust、Go、C#等多种编程语言，根据自然语言描述生成高质量代码。</p>
                    </div>
                    <div class="feature-card">
                        <h3>💬 智能对话助手</h3>
                        <p>24/7在线AI助手，回答编程问题，提供技术建议，协助学习和解决开发难题。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔒 完全本地运行</h3>
                        <p>无需联网，保护您的代码隐私和数据安全，所有处理都在本地完成。</p>
                    </div>
                    <div class="feature-card">
                        <h3>⚡ 快速响应</h3>
                        <p>本地处理，无网络延迟，即时生成代码和回答问题，提高开发效率。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🎨 现代界面</h3>
                        <p>简洁美观的用户界面，支持代码高亮，提供良好的用户体验。</p>
                    </div>
                    <div class="feature-card">
                        <h3>📚 丰富示例</h3>
                        <p>内置大量代码模板和示例，涵盖常见算法、数据结构和编程模式。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关于 -->
        <div id="about" class="tab-content">
            <div class="card">
                <h2>ℹ️ 关于本地AI编辑器</h2>

                <h3>🎯 项目简介</h3>
                <p style="margin: 15px 0; line-height: 1.6;">
                    本地AI编辑器是一个完全本地运行的智能编程助手，旨在为开发者提供高效、安全、便捷的代码生成和编程支持服务。
                    无需联网，保护您的代码隐私，让AI成为您最得力的编程伙伴。
                </p>

                <h3>🚀 技术架构</h3>
                <ul style="margin: 15px 0; padding-left: 20px; line-height: 1.8;">
                    <li><strong>前端</strong>：现代HTML5 + CSS3 + JavaScript，响应式设计</li>
                    <li><strong>AI引擎</strong>：智能模板匹配 + 规则引擎 + 语义分析</li>
                    <li><strong>代码生成</strong>：多语言支持，语法高亮，智能补全</li>
                    <li><strong>对话系统</strong>：自然语言处理，上下文理解</li>
                </ul>

                <h3>💡 使用建议</h3>
                <ul style="margin: 15px 0; padding-left: 20px; line-height: 1.8;">
                    <li>代码生成时，请详细描述您的需求和期望的功能</li>
                    <li>支持中英文输入，可以使用技术术语</li>
                    <li>生成的代码可以直接复制到您的项目中使用</li>
                    <li>遇到问题时，可以在对话中详细描述情况</li>
                    <li>建议先从简单的示例开始，逐步尝试复杂功能</li>
                </ul>

                <h3>🔧 支持的编程语言</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 10px; margin: 15px 0;">
                    <span style="background: #3776ab; color: white; padding: 5px 10px; border-radius: 15px;">🐍 Python</span>
                    <span style="background: #f7df1e; color: black; padding: 5px 10px; border-radius: 15px;">🟨 JavaScript</span>
                    <span style="background: #ed8b00; color: white; padding: 5px 10px; border-radius: 15px;">☕ Java</span>
                    <span style="background: #00599c; color: white; padding: 5px 10px; border-radius: 15px;">⚡ C++</span>
                    <span style="background: #000000; color: white; padding: 5px 10px; border-radius: 15px;">🦀 Rust</span>
                    <span style="background: #00add8; color: white; padding: 5px 10px; border-radius: 15px;">🐹 Go</span>
                    <span style="background: #239120; color: white; padding: 5px 10px; border-radius: 15px;">💙 C#</span>
                </div>

                <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; color: white; text-align: center;">
                    <h3>🎉 版本信息</h3>
                    <p style="margin: 10px 0;">版本：v2.0.0 Enhanced Edition</p>
                    <p style="margin: 10px 0;">更新日期：2025-07-13</p>
                    <p style="margin: 10px 0;">开发团队：本地AI编辑器项目组</p>
                    <p style="margin: 10px 0; font-size: 14px; opacity: 0.9;">
                        致力于为开发者提供最好的本地AI编程体验
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="status" id="status">本地AI编辑器已就绪 🚀</div>

    <script>
        // 全局变量
        let currentLanguage = 'python';

        // 切换标签页
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // 更新状态
            updateStatus(`切换到${getTabName(tabName)} 📋`);
        }

        function getTabName(tabName) {
            const names = {
                'code': '代码生成',
                'chat': '智能对话',
                'features': '功能特性',
                'about': '关于页面'
            };
            return names[tabName] || tabName;
        }

        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // 显示/隐藏加载动画
        function showLoading(elementId, show = true) {
            const loading = document.getElementById(elementId);
            if (loading) {
                loading.classList.toggle('show', show);
            }
        }

        // 设置示例
        function setExample(type) {
            const examples = {
                'hello world': '创建一个Hello World程序',
                'fibonacci': '实现斐波那契数列算法，计算前10个数',
                'sort': '编写冒泡排序算法，对数组进行排序',
                'calculator': '创建一个简单的计算器，支持加减乘除',
                'file': '创建一个文件读写程序，读取文本文件内容'
            };

            document.getElementById('codePrompt').value = examples[type] || type;
            updateStatus(`已设置示例：${examples[type]} ✨`);
        }

        // 询问问题
        function askQuestion(question) {
            document.getElementById('chatInput').value = question;
            updateStatus(`已设置问题：${question} 💭`);
        }

        // 代码生成
        function generateCode() {
            const language = document.getElementById('codeLanguage').value;
            const prompt = document.getElementById('codePrompt').value;

            if (!prompt.trim()) {
                alert('请输入代码需求描述');
                return;
            }

            currentLanguage = language;
            showLoading('codeLoading', true);
            updateStatus('AI正在生成代码... 🔄');

            // 模拟生成延迟
            setTimeout(() => {
                const code = generateCodeByTemplate(prompt, language);
                document.getElementById('codeOutput').textContent = code;
                showLoading('codeLoading', false);
                updateStatus('代码生成完成 ✅');
            }, 1500);
        }

        // 智能代码生成引擎
        function generateCodeByTemplate(prompt, language) {
            const promptLower = prompt.toLowerCase();

            // 代码模板库
            const templates = {
                python: {
                    'hello world': 'print("Hello, World!")',
                    'fibonacci': `def fibonacci(n):
    """计算斐波那契数列的第n项"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def fibonacci_sequence(count):
    """生成斐波那契数列"""
    sequence = []
    for i in range(count):
        sequence.append(fibonacci(i))
    return sequence

# 测试代码
if __name__ == "__main__":
    print("斐波那契数列前10项:")
    result = fibonacci_sequence(10)
    for i, num in enumerate(result):
        print(f"F({i}) = {num}")`,
                    'sort': `def bubble_sort(arr):
    """冒泡排序算法"""
    n = len(arr)
    for i in range(n):
        # 标记是否发生交换
        swapped = False
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
                swapped = True
        # 如果没有交换，说明已经排序完成
        if not swapped:
            break
    return arr

def quick_sort(arr):
    """快速排序算法"""
    if len(arr) <= 1:
        return arr

    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]

    return quick_sort(left) + middle + quick_sort(right)

# 测试代码
if __name__ == "__main__":
    test_array = [64, 34, 25, 12, 22, 11, 90, 5]
    print(f"原数组: {test_array}")

    # 冒泡排序
    bubble_result = bubble_sort(test_array.copy())
    print(f"冒泡排序结果: {bubble_result}")

    # 快速排序
    quick_result = quick_sort(test_array.copy())
    print(f"快速排序结果: {quick_result}")`,
                    'calculator': `class Calculator:
    """简单计算器类"""

    def add(self, a, b):
        """加法"""
        return a + b

    def subtract(self, a, b):
        """减法"""
        return a - b

    def multiply(self, a, b):
        """乘法"""
        return a * b

    def divide(self, a, b):
        """除法"""
        if b == 0:
            raise ValueError("除数不能为零")
        return a / b

    def power(self, a, b):
        """幂运算"""
        return a ** b

    def sqrt(self, a):
        """平方根"""
        if a < 0:
            raise ValueError("负数没有实数平方根")
        return a ** 0.5

def main():
    calc = Calculator()

    while True:
        print("\\n=== 简单计算器 ===")
        print("1. 加法")
        print("2. 减法")
        print("3. 乘法")
        print("4. 除法")
        print("5. 幂运算")
        print("6. 平方根")
        print("0. 退出")

        choice = input("请选择操作 (0-6): ")

        if choice == '0':
            print("感谢使用计算器！")
            break

        try:
            if choice in ['1', '2', '3', '4', '5']:
                a = float(input("请输入第一个数字: "))
                b = float(input("请输入第二个数字: "))

                if choice == '1':
                    result = calc.add(a, b)
                    print(f"结果: {a} + {b} = {result}")
                elif choice == '2':
                    result = calc.subtract(a, b)
                    print(f"结果: {a} - {b} = {result}")
                elif choice == '3':
                    result = calc.multiply(a, b)
                    print(f"结果: {a} × {b} = {result}")
                elif choice == '4':
                    result = calc.divide(a, b)
                    print(f"结果: {a} ÷ {b} = {result}")
                elif choice == '5':
                    result = calc.power(a, b)
                    print(f"结果: {a} ^ {b} = {result}")

            elif choice == '6':
                a = float(input("请输入数字: "))
                result = calc.sqrt(a)
                print(f"结果: √{a} = {result}")

            else:
                print("无效选择，请重试")

        except ValueError as e:
            print(f"错误: {e}")
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()`,
                    'file': `import os
import json
from datetime import datetime

class FileManager:
    """文件管理器类"""

    def read_text_file(self, filename):
        """读取文本文件"""
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                content = file.read()
            print(f"成功读取文件: {filename}")
            return content
        except FileNotFoundError:
            print(f"错误: 文件 {filename} 不存在")
            return None
        except Exception as e:
            print(f"读取文件时发生错误: {e}")
            return None

    def write_text_file(self, filename, content):
        """写入文本文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"成功写入文件: {filename}")
            return True
        except Exception as e:
            print(f"写入文件时发生错误: {e}")
            return False

    def append_to_file(self, filename, content):
        """追加内容到文件"""
        try:
            with open(filename, 'a', encoding='utf-8') as file:
                file.write(content)
            print(f"成功追加内容到文件: {filename}")
            return True
        except Exception as e:
            print(f"追加文件时发生错误: {e}")
            return False

    def list_files(self, directory="."):
        """列出目录中的文件"""
        try:
            files = os.listdir(directory)
            print(f"目录 {directory} 中的文件:")
            for file in files:
                file_path = os.path.join(directory, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  📄 {file} ({size} bytes)")
                elif os.path.isdir(file_path):
                    print(f"  📁 {file}/")
            return files
        except Exception as e:
            print(f"列出文件时发生错误: {e}")
            return []

def main():
    fm = FileManager()

    # 示例用法
    print("=== 文件操作示例 ===")

    # 创建示例文件
    sample_content = f"""这是一个示例文件
创建时间: {datetime.now()}
内容: Hello, World!
这是第二行内容。
"""

    filename = "sample.txt"

    # 写入文件
    if fm.write_text_file(filename, sample_content):
        print("\\n文件创建成功！")

    # 读取文件
    content = fm.read_text_file(filename)
    if content:
        print("\\n文件内容:")
        print("-" * 30)
        print(content)
        print("-" * 30)

    # 追加内容
    append_content = f"\\n追加时间: {datetime.now()}\\n追加内容: 这是追加的内容。"
    fm.append_to_file(filename, append_content)

    # 再次读取文件
    updated_content = fm.read_text_file(filename)
    if updated_content:
        print("\\n更新后的文件内容:")
        print("-" * 30)
        print(updated_content)
        print("-" * 30)

    # 列出当前目录文件
    print("\\n当前目录文件列表:")
    fm.list_files()

if __name__ == "__main__":
    main()`,
                    'default': `# ${language.toUpperCase()} 代码示例
# 需求: ${prompt}

def main():
    """
    根据您的需求: ${prompt}

    这是一个代码框架，请根据具体需求进行修改和完善。
    """
    print("AI编辑器生成的代码框架")

    # TODO: 在这里实现您的具体功能
    # 1. 分析需求
    # 2. 设计算法
    # 3. 编写代码
    # 4. 测试验证

    pass

if __name__ == "__main__":
    main()`
                }
            };

            // 获取对应语言的模板
            const langTemplates = templates[language] || templates.python;

            // 智能匹配关键词
            for (const [key, template] of Object.entries(langTemplates)) {
                if (key !== 'default' && promptLower.includes(key)) {
                    return template;
                }
            }

            // 返回默认模板
            return langTemplates.default.replace(/\$\{language\}/g, language).replace(/\$\{prompt\}/g, prompt);
        }

        // 复制代码
        function copyCode() {
            const codeOutput = document.getElementById('codeOutput');
            const code = codeOutput.textContent;

            if (!code || code.includes('生成的代码将显示在这里')) {
                alert('没有可复制的代码');
                return;
            }

            navigator.clipboard.writeText(code).then(() => {
                updateStatus('代码已复制到剪贴板 📋');

                // 临时改变按钮文本
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '✅ 已复制';
                setTimeout(() => {
                    btn.textContent = originalText;
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择代码');
            });
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message) {
                alert('请输入消息');
                return;
            }

            const chatOutput = document.getElementById('chatOutput');

            // 添加用户消息
            chatOutput.innerHTML += `
                <div class="chat-message user">
                    <strong>👤 您:</strong> ${message}
                </div>
            `;

            input.value = '';
            chatOutput.scrollTop = chatOutput.scrollHeight;
            updateStatus('AI正在思考... 🤔');

            // 模拟AI思考时间
            setTimeout(() => {
                const response = generateChatResponse(message);
                chatOutput.innerHTML += `
                    <div class="chat-message assistant">
                        <strong>🤖 AI助手:</strong> ${response}
                    </div>
                `;
                chatOutput.scrollTop = chatOutput.scrollHeight;
                updateStatus('对话完成 ✅');
            }, 1200);
        }

        // 智能对话响应生成
        function generateChatResponse(message) {
            const messageLower = message.toLowerCase();

            // 智能响应库
            const responses = {
                '你好': '您好！我是您的本地AI编程助手，很高兴为您服务！有什么编程问题需要帮助吗？',
                'hello': 'Hello! I am your local AI programming assistant. How can I help you with coding today?',
                '如何学习编程': `学习编程的建议路径：

🎯 **选择语言**: 初学者推荐Python，简单易学
📚 **基础知识**: 变量、数据类型、控制结构、函数
🔧 **实践项目**: 从小项目开始，逐步增加复杂度
💡 **算法思维**: 学习常见算法和数据结构
🌐 **实际应用**: Web开发、数据分析、人工智能等
📖 **持续学习**: 关注技术趋势，不断提升技能

记住：编程是一门实践性很强的技能，多写代码是关键！`,

                '什么是算法': `算法是解决问题的步骤和方法：

🔍 **定义**: 解决特定问题的有限步骤序列
⚡ **特性**: 有穷性、确定性、可行性、输入输出
📊 **分类**:
   • 排序算法：冒泡、快排、归并等
   • 搜索算法：二分查找、深度优先等
   • 动态规划：最优子结构问题
   • 贪心算法：局部最优解

💡 **学习建议**: 理解思想比记忆代码更重要`,

                '如何选择编程语言': `选择编程语言的考虑因素：

🎯 **目标导向**:
   • Web开发: JavaScript, Python, PHP
   • 移动开发: Java/Kotlin (Android), Swift (iOS)
   • 数据科学: Python, R
   • 系统编程: C++, Rust, Go
   • 游戏开发: C#, C++

👶 **初学者推荐**: Python - 语法简洁，应用广泛
🚀 **进阶选择**: 根据职业方向和项目需求决定
📈 **市场需求**: 关注行业趋势和就业机会`,

                '代码优化技巧': `代码优化的实用技巧：

⚡ **性能优化**:
   • 选择合适的数据结构和算法
   • 避免不必要的循环和递归
   • 使用缓存减少重复计算

📝 **代码质量**:
   • 使用有意义的变量名
   • 函数保持单一职责
   • 添加适当的注释
   • 遵循编码规范

🔧 **工具辅助**:
   • 使用代码分析工具
   • 进行单元测试
   • 版本控制管理

记住：过早优化是万恶之源，先保证正确性再考虑性能！`,

                'python': `Python是一种优秀的编程语言：

🐍 **特点**:
   • 语法简洁易读
   • 丰富的标准库
   • 强大的第三方生态
   • 跨平台支持

🚀 **应用领域**:
   • Web开发 (Django, Flask)
   • 数据科学 (NumPy, Pandas)
   • 人工智能 (TensorFlow, PyTorch)
   • 自动化脚本

💡 **学习建议**: 从基础语法开始，多做实际项目`,

                'javascript': `JavaScript是Web开发的核心语言：

🟨 **特点**:
   • 动态类型语言
   • 事件驱动编程
   • 异步编程支持
   • 函数式编程特性

🌐 **应用场景**:
   • 前端交互 (React, Vue, Angular)
   • 后端开发 (Node.js)
   • 移动应用 (React Native)
   • 桌面应用 (Electron)

📚 **学习路径**: HTML/CSS → JavaScript基础 → 框架学习`,

                '递归': `递归是一种重要的编程技巧：

🔄 **定义**: 函数调用自身来解决问题
📋 **要素**:
   • 基础情况 (递归终止条件)
   • 递归情况 (问题分解)

💡 **经典例子**:
   • 阶乘计算
   • 斐波那契数列
   • 树的遍历
   • 汉诺塔问题

⚠️ **注意事项**: 避免无限递归，考虑栈溢出问题`,

                '数据结构': `数据结构是编程的基础：

📊 **基本类型**:
   • 数组: 连续存储，随机访问
   • 链表: 动态大小，插入删除高效
   • 栈: 后进先出 (LIFO)
   • 队列: 先进先出 (FIFO)
   • 树: 层次结构，搜索高效
   • 图: 复杂关系建模

🎯 **选择原则**: 根据操作需求选择合适的数据结构
💡 **学习建议**: 理解原理，掌握应用场景`
            };

            // 智能匹配响应
            for (const [key, response] of Object.entries(responses)) {
                if (messageLower.includes(key)) {
                    return response;
                }
            }

            // 默认智能响应
            if (messageLower.includes('代码') || messageLower.includes('编程') || messageLower.includes('程序')) {
                return `关于"${message}"的问题，我建议您：

1. 🎯 **明确需求**: 详细描述您想要实现的功能
2. 💻 **选择语言**: 根据项目需求选择合适的编程语言
3. 🔧 **分步实现**: 将复杂问题分解为小的模块
4. 🧪 **测试验证**: 编写测试用例确保代码正确性

如果您需要具体的代码实现，请切换到"代码生成"标签页，详细描述您的需求。`;
            }

            return `感谢您的问题："${message}"。

作为本地AI编程助手，我可以帮您：
• 🔧 生成各种编程语言的代码
• 💡 解答编程相关问题
• 🚀 提供技术建议和最佳实践
• 🐛 协助调试和优化代码

请告诉我您具体需要什么帮助，我会尽力为您提供专业的建议！`;
        }

        // 清空对话
        function clearChat() {
            document.getElementById('chatOutput').innerHTML = `
                <div class="chat-message assistant">
                    <strong>🤖 AI助手:</strong> 对话已清空。有什么新的问题吗？我随时为您提供编程帮助！
                </div>
            `;
            updateStatus('对话已清空 🗑️');
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            updateStatus('本地AI编辑器已就绪，开始您的编程之旅吧！ 🚀');

            // 添加一些交互提示
            setTimeout(() => {
                updateStatus('💡 提示：点击快速示例按钮试试看！');
            }, 3000);
        });

        // 监听语言选择变化
        document.getElementById('codeLanguage').addEventListener('change', function() {
            currentLanguage = this.value;
            const languageNames = {
                'python': 'Python 🐍',
                'javascript': 'JavaScript 🟨',
                'java': 'Java ☕',
                'cpp': 'C++ ⚡',
                'rust': 'Rust 🦀',
                'go': 'Go 🐹',
                'csharp': 'C# 💙'
            };
            updateStatus(`已选择 ${languageNames[this.value]} 编程语言`);
        });
    </script>
</body>
</html>