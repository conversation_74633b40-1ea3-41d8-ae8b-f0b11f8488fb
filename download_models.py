#!/usr/bin/env python3
"""
下载本地AI编辑器所需的模型
"""

import os
import sys
from pathlib import Path
import logging
from huggingface_hub import snapshot_download, hf_hub_download
import torch

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 模型存储目录
MODEL_DIR = Path("E:/ai_models")
MODEL_DIR.mkdir(exist_ok=True)

# 推荐的模型列表
MODELS = {
    "代码生成": {
        "small": "microsoft/DialoGPT-medium",  # 备用小模型
        "medium": "deepseek-ai/deepseek-coder-6.7b-instruct",  # 中等模型
        "large": "deepseek-ai/deepseek-coder-33b-instruct"  # 大模型
    },
    "对话聊天": {
        "small": "microsoft/DialoGPT-large",
        "medium": "Qwen/Qwen2.5-7B-Instruct", 
        "large": "Qwen/Qwen2.5-32B-Instruct"
    },
    "图像生成": {
        "small": "runwayml/stable-diffusion-v1-5",
        "medium": "stabilityai/stable-diffusion-xl-base-1.0",
        "large": "black-forest-labs/FLUX.1-dev"
    },
    "视频生成": {
        "small": "ali-vilab/text-to-video-ms-1.7b",
        "medium": "THUDM/CogVideoX-2b",
        "large": "THUDM/CogVideoX-5b"
    },
    "语音合成": {
        "small": "microsoft/speecht5_tts",
        "medium": "coqui/XTTS-v2",
        "large": "suno/bark"
    }
}

def check_disk_space():
    """检查磁盘空间"""
    import shutil
    total, used, free = shutil.disk_usage(MODEL_DIR)
    free_gb = free // (1024**3)
    logger.info(f"E盘可用空间: {free_gb}GB")
    return free_gb

def download_model(model_name, model_type, size):
    """下载单个模型"""
    try:
        logger.info(f"开始下载 {model_type} ({size}): {model_name}")
        
        # 创建模型特定目录
        model_path = MODEL_DIR / model_type / size / model_name.replace("/", "_")
        model_path.mkdir(parents=True, exist_ok=True)
        
        # 下载模型
        snapshot_download(
            repo_id=model_name,
            cache_dir=str(model_path),
            resume_download=True,
            local_files_only=False
        )
        
        logger.info(f"✅ {model_name} 下载完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ {model_name} 下载失败: {e}")
        return False

def download_essential_models():
    """下载基础必需模型"""
    logger.info("🚀 开始下载基础AI模型...")
    
    # 检查磁盘空间
    free_space = check_disk_space()
    if free_space < 50:
        logger.warning("⚠️ 磁盘空间不足50GB，建议清理后再下载大模型")
    
    # 根据可用空间选择模型大小
    if free_space > 500:
        size_preference = ["large", "medium", "small"]
        logger.info("💪 检测到充足存储空间，将下载大模型")
    elif free_space > 200:
        size_preference = ["medium", "small"]
        logger.info("📦 存储空间适中，将下载中等模型")
    else:
        size_preference = ["small"]
        logger.info("💾 存储空间有限，将下载小模型")
    
    success_count = 0
    total_count = 0
    
    # 下载每个类型的首选模型
    for model_type, models in MODELS.items():
        for size in size_preference:
            if size in models:
                total_count += 1
                if download_model(models[size], model_type, size):
                    success_count += 1
                break  # 每个类型只下载一个大小的模型
    
    logger.info(f"📊 下载完成: {success_count}/{total_count} 个模型成功")
    return success_count, total_count

def download_specific_model(model_type, size):
    """下载特定模型"""
    if model_type not in MODELS:
        logger.error(f"未知模型类型: {model_type}")
        return False
    
    if size not in MODELS[model_type]:
        logger.error(f"未知模型大小: {size}")
        return False
    
    model_name = MODELS[model_type][size]
    return download_model(model_name, model_type, size)

def list_available_models():
    """列出可用模型"""
    print("\n🤖 可用模型列表:")
    print("=" * 60)
    
    for model_type, models in MODELS.items():
        print(f"\n📂 {model_type}:")
        for size, model_name in models.items():
            print(f"  {size:8} - {model_name}")

def check_downloaded_models():
    """检查已下载的模型"""
    print("\n📦 已下载模型:")
    print("=" * 60)
    
    if not MODEL_DIR.exists():
        print("❌ 模型目录不存在")
        return
    
    for model_type_dir in MODEL_DIR.iterdir():
        if model_type_dir.is_dir():
            print(f"\n📂 {model_type_dir.name}:")
            for size_dir in model_type_dir.iterdir():
                if size_dir.is_dir():
                    for model_dir in size_dir.iterdir():
                        if model_dir.is_dir():
                            size_mb = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file()) / (1024*1024)
                            print(f"  ✅ {size_dir.name} - {model_dir.name} ({size_mb:.1f}MB)")

def main():
    """主函数"""
    print("🚀 本地最强AI编辑器 - 模型下载工具")
    print("=" * 60)
    
    if len(sys.argv) == 1:
        # 无参数，显示菜单
        while True:
            print("\n请选择操作:")
            print("1. 下载基础模型包（推荐）")
            print("2. 列出可用模型")
            print("3. 检查已下载模型")
            print("4. 下载特定模型")
            print("5. 检查系统信息")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-5): ").strip()
            
            if choice == "1":
                download_essential_models()
            elif choice == "2":
                list_available_models()
            elif choice == "3":
                check_downloaded_models()
            elif choice == "4":
                list_available_models()
                model_type = input("\n请输入模型类型: ").strip()
                size = input("请输入模型大小 (small/medium/large): ").strip()
                download_specific_model(model_type, size)
            elif choice == "5":
                print(f"\n💻 系统信息:")
                print(f"Python版本: {sys.version}")
                print(f"PyTorch版本: {torch.__version__}")
                print(f"CUDA可用: {torch.cuda.is_available()}")
                if torch.cuda.is_available():
                    print(f"GPU: {torch.cuda.get_device_name(0)}")
                    print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
                check_disk_space()
            elif choice == "0":
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重试")
    
    elif len(sys.argv) == 2:
        if sys.argv[1] == "download":
            download_essential_models()
        elif sys.argv[1] == "list":
            list_available_models()
        elif sys.argv[1] == "check":
            check_downloaded_models()
        else:
            print("用法: python download_models.py [download|list|check]")
    
    else:
        print("用法: python download_models.py [download|list|check]")

if __name__ == "__main__":
    main()
