Write-Host "Setting up development environment..." -ForegroundColor Green

# Get current user PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

# Add Rust to PATH
$rustPath = "$env:USERPROFILE\.cargo\bin"
if (Test-Path $rustPath) {
    if ($currentPath -notlike "*$rustPath*") {
        $newPath = "$currentPath;$rustPath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "Rust path added to PATH" -ForegroundColor Green
    } else {
        Write-Host "Rust path already exists in PATH" -ForegroundColor Yellow
    }
} else {
    Write-Host "Rust installation path not found" -ForegroundColor Red
}

# Add Java to PATH
$javaPath = "$env:USERPROFILE\Java\jdk-21.0.2\bin"
if (Test-Path $javaPath) {
    $currentUserPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($currentUserPath -notlike "*$javaPath*") {
        $newPath = "$currentUserPath;$javaPath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "Java path added to PATH" -ForegroundColor Green
    } else {
        Write-Host "Java path already exists in PATH" -ForegroundColor Yellow
    }
    
    # Set JAVA_HOME
    $javaHome = "$env:USERPROFILE\Java\jdk-21.0.2"
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHome, "User")
    Write-Host "JAVA_HOME set" -ForegroundColor Green
} else {
    Write-Host "Java installation path not found" -ForegroundColor Red
}

# Add Go to PATH
$goPath = "$env:USERPROFILE\go\bin"
if (Test-Path $goPath) {
    $currentUserPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($currentUserPath -notlike "*$goPath*") {
        $newPath = "$currentUserPath;$goPath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "Go path added to PATH" -ForegroundColor Green
    } else {
        Write-Host "Go path already exists in PATH" -ForegroundColor Yellow
    }

    # Set GOROOT
    $goRoot = "$env:USERPROFILE\go"
    [Environment]::SetEnvironmentVariable("GOROOT", $goRoot, "User")
    Write-Host "GOROOT set" -ForegroundColor Green
} else {
    Write-Host "Go installation path not found" -ForegroundColor Red
}

# Add Node.js to PATH
$nodePath = "$env:USERPROFILE\node-v20.18.1-win-x64"
if (Test-Path $nodePath) {
    $currentUserPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($currentUserPath -notlike "*$nodePath*") {
        $newPath = "$currentUserPath;$nodePath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "Node.js path added to PATH" -ForegroundColor Green
    } else {
        Write-Host "Node.js path already exists in PATH" -ForegroundColor Yellow
    }
} else {
    Write-Host "Node.js installation path not found" -ForegroundColor Red
}

Write-Host "Environment setup complete!" -ForegroundColor Green
Write-Host "Please restart PowerShell to apply changes." -ForegroundColor Cyan

# Check installed versions
Write-Host "`nChecking installed versions:" -ForegroundColor Cyan

if (Test-Path "$env:USERPROFILE\.cargo\bin\rustc.exe") {
    $rustVersion = & "$env:USERPROFILE\.cargo\bin\rustc.exe" --version
    Write-Host "Rust: $rustVersion" -ForegroundColor Green
}

if (Test-Path "$env:USERPROFILE\Java\jdk-21.0.2\bin\java.exe") {
    $javaVersion = & "$env:USERPROFILE\Java\jdk-21.0.2\bin\java.exe" -version 2>&1 | Select-Object -First 1
    Write-Host "Java: $javaVersion" -ForegroundColor Green
}

if (Test-Path "$env:USERPROFILE\go\bin\go.exe") {
    $goVersion = & "$env:USERPROFILE\go\bin\go.exe" version
    Write-Host "Go: $goVersion" -ForegroundColor Green
}

if (Test-Path "$env:USERPROFILE\node-v20.18.1-win-x64\node.exe") {
    $nodeVersion = & "$env:USERPROFILE\node-v20.18.1-win-x64\node.exe" --version
    Write-Host "Node.js: $nodeVersion" -ForegroundColor Green
}

$dotnetVersion = dotnet --version 2>$null
if ($dotnetVersion) {
    Write-Host ".NET: $dotnetVersion" -ForegroundColor Green
}
