#!/usr/bin/env python3
"""
清理旧模型和缓存
"""

import os
import shutil
import sys
from pathlib import Path

def cleanup_huggingface_cache():
    """清理Hugging Face缓存"""
    print("🧹 清理Hugging Face模型缓存...")
    
    cache_dirs = [
        Path.home() / ".cache" / "huggingface",
        Path("E:/huggingface_models"),
        Path("E:/ultimate_ai_models"),
        Path("generated_anime_videos"),
    ]
    
    total_freed = 0
    
    for cache_dir in cache_dirs:
        if cache_dir.exists():
            try:
                size = sum(f.stat().st_size for f in cache_dir.rglob('*') if f.is_file())
                size_gb = size / (1024**3)
                
                print(f"删除: {cache_dir} ({size_gb:.1f}GB)")
                shutil.rmtree(cache_dir)
                total_freed += size_gb
                
            except Exception as e:
                print(f"❌ 删除失败 {cache_dir}: {e}")
    
    print(f"✅ 清理完成，释放空间: {total_freed:.1f}GB")

def cleanup_temp_files():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    
    temp_patterns = [
        "*.tmp",
        "*.log", 
        "*_report.json",
        "model_info.md",
        "character_*.png",
        "scene_*.png",
        "clip_*.mp4"
    ]
    
    current_dir = Path(".")
    for pattern in temp_patterns:
        for file in current_dir.glob(pattern):
            try:
                file.unlink()
                print(f"删除: {file}")
            except Exception as e:
                print(f"❌ 删除失败 {file}: {e}")

def main():
    print("🚀 清理旧模型和缓存")
    print("=" * 50)
    
    cleanup_huggingface_cache()
    cleanup_temp_files()
    
    print("\n✅ 清理完成，准备安装新的强大模型")

if __name__ == "__main__":
    main()
