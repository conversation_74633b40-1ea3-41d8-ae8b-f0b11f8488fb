Write-Host "正在设置开发环境变量..." -ForegroundColor Green

# 获取当前用户的 PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

# 添加 Rust 到 PATH
$rustPath = "$env:USERPROFILE\.cargo\bin"
if (Test-Path $rustPath) {
    if ($currentPath -notlike "*$rustPath*") {
        $newPath = "$currentPath;$rustPath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "✓ Rust 路径已添加到 PATH" -ForegroundColor Green
    } else {
        Write-Host "✓ Rust 路径已存在于 PATH 中" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ Rust 安装路径不存在" -ForegroundColor Red
}

# 添加 Java 到 PATH
$javaPath = "$env:USERPROFILE\Java\jdk-21.0.2\bin"
if (Test-Path $javaPath) {
    $currentUserPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($currentUserPath -notlike "*$javaPath*") {
        $newPath = "$currentUserPath;$javaPath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "✓ Java 路径已添加到 PATH" -ForegroundColor Green
    } else {
        Write-Host "✓ Java 路径已存在于 PATH 中" -ForegroundColor Yellow
    }

    # 设置 JAVA_HOME
    [Environment]::SetEnvironmentVariable("JAVA_HOME", "$env:USERPROFILE\Java\jdk-21.0.2", "User")
    Write-Host "✓ JAVA_HOME 已设置" -ForegroundColor Green
} else {
    Write-Host "✗ Java 安装路径不存在" -ForegroundColor Red
}

# 检查 Go 安装
$goPath = "C:\Program Files\Go\bin"
if (Test-Path $goPath) {
    $currentUserPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($currentUserPath -notlike "*$goPath*") {
        $newPath = "$currentUserPath;$goPath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "✓ Go 路径已添加到 PATH" -ForegroundColor Green
    } else {
        Write-Host "✓ Go 路径已存在于 PATH 中" -ForegroundColor Yellow
    }

    [Environment]::SetEnvironmentVariable("GOROOT", "C:\Program Files\Go", "User")
    Write-Host "✓ GOROOT 已设置" -ForegroundColor Green
} else {
    Write-Host "✗ Go 安装路径不存在，可能需要重新安装" -ForegroundColor Red
}

Write-Host "`n环境变量设置完成！" -ForegroundColor Green
Write-Host "请重新启动 PowerShell 或命令提示符以使更改生效。" -ForegroundColor Cyan

# 显示当前安装的工具版本
Write-Host "`n检查已安装的工具版本：" -ForegroundColor Cyan

if (Test-Path "$env:USERPROFILE\.cargo\bin\rustc.exe") {
    $rustVersion = & "$env:USERPROFILE\.cargo\bin\rustc.exe" --version
    Write-Host "Rust: $rustVersion" -ForegroundColor Green
}

if (Test-Path "$env:USERPROFILE\Java\jdk-21.0.2\bin\java.exe") {
    $javaVersion = & "$env:USERPROFILE\Java\jdk-21.0.2\bin\java.exe" -version 2>&1 | Select-Object -First 1
    Write-Host "Java: $javaVersion" -ForegroundColor Green
}

if (Test-Path "C:\Program Files\Go\bin\go.exe") {
    $goVersion = & "C:\Program Files\Go\bin\go.exe" version
    Write-Host "Go: $goVersion" -ForegroundColor Green
}

$dotnetVersion = dotnet --version 2>$null
if ($dotnetVersion) {
    Write-Host ".NET: $dotnetVersion" -ForegroundColor Green
}
