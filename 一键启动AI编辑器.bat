@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
title 本地AI编辑器 - 一键启动

echo.
echo ████████████████████████████████████████
echo 🚀 本地最强AI编辑器 - 一键启动
echo ████████████████████████████████████████
echo.
echo 功能特性:
echo ✅ 代码生成 (Python, JavaScript, Java, C++)
echo ✅ 智能对话 (支持中英文)
echo ✅ 完全本地运行，无需联网
echo ✅ 支持GPU加速 (如果可用)
echo.

REM 检测Python
echo 🔍 检测Python环境...
set PYTHON_FOUND=0
set PYTHON_CMD=

REM 尝试不同的Python命令
for %%P in (python python3 py) do (
    if !PYTHON_FOUND! == 0 (
        %%P --version >nul 2>&1
        if !errorlevel! == 0 (
            set PYTHON_CMD=%%P
            set PYTHON_FOUND=1
            echo ✅ 找到Python: %%P
        )
    )
)

REM 尝试具体路径
if !PYTHON_FOUND! == 0 (
    for %%P in ("%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe" "%USERPROFILE%\AppData\Local\Programs\Python\Python311\python.exe" "%USERPROFILE%\AppData\Local\Programs\Python\Python310\python.exe") do (
        if !PYTHON_FOUND! == 0 (
            if exist %%P (
                %%P --version >nul 2>&1
                if !errorlevel! == 0 (
                    set PYTHON_CMD=%%P
                    set PYTHON_FOUND=1
                    echo ✅ 找到Python: %%P
                )
            )
        )
    )
)

if !PYTHON_FOUND! == 0 (
    echo ❌ 未找到Python！
    echo.
    echo 请先安装Python:
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装最新版本
    echo 3. 安装时勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo.
echo 📦 安装/更新依赖包...
echo 正在安装基础依赖...
!PYTHON_CMD! -m pip install --upgrade pip --quiet
!PYTHON_CMD! -m pip install fastapi uvicorn pydantic --quiet

echo 正在安装AI依赖 (可能需要几分钟)...
!PYTHON_CMD! -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121 --quiet
!PYTHON_CMD! -m pip install transformers --quiet

echo.
echo 🎯 选择启动模式:
echo 1. 简化版 (快速启动，模拟AI功能)
echo 2. 增强版 (真实AI模型，需要下载模型)
echo 3. 退出
echo.
set /p choice="请选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🚀 启动简化版AI编辑器...
    echo 📱 浏览器将自动打开
    echo.
    !PYTHON_CMD! simple_ai_editor.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 启动增强版AI编辑器...
    echo 📱 首次启动会下载AI模型，请耐心等待
    echo.
    !PYTHON_CMD! enhanced_ai_editor.py
) else if "%choice%"=="3" (
    echo 👋 再见！
    exit /b 0
) else (
    echo ❌ 无效选择，启动简化版...
    !PYTHON_CMD! simple_ai_editor.py
)

echo.
echo 服务已停止
pause
