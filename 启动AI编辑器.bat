@echo off
chcp 65001 >nul
title 本地AI编辑器启动器

echo.
echo ========================================
echo 🚀 本地AI编辑器启动器
echo ========================================
echo.

REM 尝试不同的Python路径
set PYTHON_PATHS=python python3 py "%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe" "%USERPROFILE%\AppData\Local\Programs\Python\Python311\python.exe" "%USERPROFILE%\AppData\Local\Programs\Python\Python310\python.exe"

set PYTHON_FOUND=0

for %%P in (%PYTHON_PATHS%) do (
    if !PYTHON_FOUND! == 0 (
        echo 尝试Python路径: %%P
        %%P --version >nul 2>&1
        if !errorlevel! == 0 (
            echo ✅ 找到Python: %%P
            set PYTHON_CMD=%%P
            set PYTHON_FOUND=1
        )
    )
)

if %PYTHON_FOUND% == 0 (
    echo ❌ 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo.
echo 📦 检查并安装依赖...
%PYTHON_CMD% -m pip install fastapi uvicorn pydantic --quiet

echo.
echo 🚀 启动AI编辑器...
echo 浏览器将自动打开: http://localhost:8000/app
echo.

%PYTHON_CMD% simple_ai_editor.py

pause
