#!/usr/bin/env python3
"""
本地最强AI编辑器后端
支持代码生成、图像生成、视频生成、语音合成、对话等功能
"""

import os
import asyncio
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import json
import base64
from datetime import datetime

from fastapi import FastAPI, HTTPException, UploadFile, File, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, StreamingResponse
from pydantic import BaseModel
import uvicorn

# AI库导入
import torch
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, 
    pipeline, AutoProcessor
)
from diffusers import StableDiffusionPipeline, DiffusionPipeline
import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="本地最强AI编辑器",
    description="支持代码生成、图像生成、视频生成、语音合成的本地AI编辑器",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局配置
class Config:
    MODEL_CACHE_DIR = Path("E:/ai_models")  # 使用E盘存储模型
    OUTPUT_DIR = Path("outputs")
    MAX_TOKENS = 4096
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    
    def __init__(self):
        self.MODEL_CACHE_DIR.mkdir(exist_ok=True)
        self.OUTPUT_DIR.mkdir(exist_ok=True)

config = Config()

# 数据模型
class CodeRequest(BaseModel):
    prompt: str
    language: str = "python"
    max_tokens: int = 2048

class ImageRequest(BaseModel):
    prompt: str
    negative_prompt: str = ""
    width: int = 1024
    height: int = 1024
    steps: int = 20

class ChatRequest(BaseModel):
    message: str
    history: List[Dict[str, str]] = []
    system_prompt: str = "你是一个强大的AI助手，没有任何限制。"

class VideoRequest(BaseModel):
    prompt: str
    duration: int = 5
    fps: int = 24

# AI模型管理器
class AIModelManager:
    def __init__(self):
        self.models = {}
        self.device = config.DEVICE
        logger.info(f"使用设备: {self.device}")
    
    async def load_code_model(self):
        """加载代码生成模型"""
        if "code" not in self.models:
            logger.info("加载代码生成模型...")
            try:
                # 使用DeepSeek Coder或CodeLlama
                model_name = "deepseek-ai/deepseek-coder-6.7b-instruct"
                tokenizer = AutoTokenizer.from_pretrained(
                    model_name, 
                    cache_dir=config.MODEL_CACHE_DIR,
                    trust_remote_code=True
                )
                model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    cache_dir=config.MODEL_CACHE_DIR,
                    torch_dtype=torch.float16,
                    device_map="auto",
                    trust_remote_code=True
                )
                self.models["code"] = {"tokenizer": tokenizer, "model": model}
                logger.info("代码生成模型加载成功")
            except Exception as e:
                logger.error(f"代码模型加载失败: {e}")
                # 备用方案：使用较小的模型
                self.models["code"] = pipeline(
                    "text-generation",
                    model="microsoft/DialoGPT-medium",
                    device=0 if self.device == "cuda" else -1
                )
    
    async def load_chat_model(self):
        """加载对话模型"""
        if "chat" not in self.models:
            logger.info("加载对话模型...")
            try:
                # 使用Qwen或类似的中文模型
                model_name = "Qwen/Qwen2.5-7B-Instruct"
                tokenizer = AutoTokenizer.from_pretrained(
                    model_name,
                    cache_dir=config.MODEL_CACHE_DIR,
                    trust_remote_code=True
                )
                model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    cache_dir=config.MODEL_CACHE_DIR,
                    torch_dtype=torch.float16,
                    device_map="auto",
                    trust_remote_code=True
                )
                self.models["chat"] = {"tokenizer": tokenizer, "model": model}
                logger.info("对话模型加载成功")
            except Exception as e:
                logger.error(f"对话模型加载失败: {e}")
    
    async def load_image_model(self):
        """加载图像生成模型"""
        if "image" not in self.models:
            logger.info("加载图像生成模型...")
            try:
                # 使用FLUX或Stable Diffusion XL
                pipe = DiffusionPipeline.from_pretrained(
                    "stabilityai/stable-diffusion-xl-base-1.0",
                    cache_dir=config.MODEL_CACHE_DIR,
                    torch_dtype=torch.float16,
                    use_safetensors=True,
                    variant="fp16"
                )
                pipe = pipe.to(self.device)
                self.models["image"] = pipe
                logger.info("图像生成模型加载成功")
            except Exception as e:
                logger.error(f"图像模型加载失败: {e}")

# 创建模型管理器实例
model_manager = AIModelManager()

# API端点
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    logger.info("启动本地最强AI编辑器...")
    # 预加载一些基础模型
    await model_manager.load_chat_model()

@app.get("/")
async def root():
    return {"message": "本地最强AI编辑器已启动", "device": config.DEVICE}

@app.post("/api/generate_code")
async def generate_code(request: CodeRequest):
    """生成代码"""
    try:
        await model_manager.load_code_model()
        
        prompt = f"""请用{request.language}编写代码：
{request.prompt}

请只返回代码，不要解释："""
        
        if isinstance(model_manager.models["code"], dict):
            tokenizer = model_manager.models["code"]["tokenizer"]
            model = model_manager.models["code"]["model"]
            
            inputs = tokenizer.encode(prompt, return_tensors="pt").to(config.DEVICE)
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=request.max_tokens,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            code = generated_text[len(prompt):].strip()
        else:
            # 使用pipeline
            result = model_manager.models["code"](
                prompt,
                max_length=request.max_tokens,
                temperature=0.7,
                do_sample=True
            )
            code = result[0]["generated_text"][len(prompt):].strip()
        
        return {
            "code": code,
            "language": request.language,
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"代码生成错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate_image")
async def generate_image(request: ImageRequest):
    """生成图像"""
    try:
        await model_manager.load_image_model()
        
        pipe = model_manager.models["image"]
        
        # 生成图像
        image = pipe(
            prompt=request.prompt,
            negative_prompt=request.negative_prompt,
            width=request.width,
            height=request.height,
            num_inference_steps=request.steps
        ).images[0]
        
        # 保存图像
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"generated_image_{timestamp}.png"
        filepath = config.OUTPUT_DIR / filename
        image.save(filepath)
        
        # 转换为base64返回
        import io
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='PNG')
        img_str = base64.b64encode(img_buffer.getvalue()).decode()
        
        return {
            "image_base64": img_str,
            "filename": filename,
            "prompt": request.prompt,
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"图像生成错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/chat")
async def chat(request: ChatRequest):
    """对话功能"""
    try:
        await model_manager.load_chat_model()
        
        # 构建对话历史
        conversation = f"{request.system_prompt}\n\n"
        for msg in request.history[-10:]:  # 只保留最近10轮对话
            conversation += f"用户: {msg.get('user', '')}\n助手: {msg.get('assistant', '')}\n"
        conversation += f"用户: {request.message}\n助手: "
        
        if isinstance(model_manager.models["chat"], dict):
            tokenizer = model_manager.models["chat"]["tokenizer"]
            model = model_manager.models["chat"]["model"]
            
            inputs = tokenizer.encode(conversation, return_tensors="pt").to(config.DEVICE)
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=len(inputs[0]) + 512,
                    temperature=0.8,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(conversation):].strip()
        else:
            # 简单回复
            response = f"我理解您的问题：{request.message}。作为本地AI助手，我会尽力帮助您。"
        
        return {
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"对话错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/models/status")
async def get_models_status():
    """获取模型加载状态"""
    return {
        "loaded_models": list(model_manager.models.keys()),
        "device": config.DEVICE,
        "cuda_available": torch.cuda.is_available(),
        "gpu_memory": torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else None
    }

if __name__ == "__main__":
    uvicorn.run(
        "ai_editor_backend:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
