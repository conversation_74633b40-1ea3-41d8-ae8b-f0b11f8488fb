#!/usr/bin/env python3
"""
快速下载并测试一个小的Hugging Face模型
"""

import os
import sys
from pathlib import Path

print("🚀 快速下载并测试Hugging Face模型")
print("=" * 50)

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
    import torch
    print("✅ AI库导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

# 检查系统信息
print(f"Python版本: {sys.version.split()[0]}")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print()

# 选择一个小模型进行测试
model_name = "microsoft/DialoGPT-small"
print(f"📦 下载模型: {model_name}")
print("这是一个约300MB的小型对话模型，适合快速测试...")

try:
    # 下载并加载模型
    print("正在下载tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    print("正在下载模型...")
    model = AutoModelForCausalLM.from_pretrained(model_name)
    
    print("✅ 模型下载和加载成功！")
    
    # 设置pad_token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # 测试对话功能
    print("\n🧪 测试模型对话功能:")
    print("-" * 30)
    
    test_messages = [
        "Hello, how are you?",
        "What is programming?", 
        "Can you help me with coding?"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n测试 {i}: {message}")
        
        # 编码输入
        inputs = tokenizer.encode(message + tokenizer.eos_token, return_tensors="pt")
        
        # 生成响应
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_length=inputs.shape[1] + 30,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # 解码响应
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        ai_response = response[len(message):].strip()
        
        print(f"AI回复: {ai_response}")
    
    print("\n" + "=" * 50)
    print("🎉 真实AI模型测试成功！")
    print("✅ 您现在拥有了一个真正的本地AI模型")
    print("📍 模型已缓存到本地，下次使用会更快")
    
    # 保存模型信息
    model_info = f"""
# 🤖 已下载的AI模型信息

## 模型详情
- **名称**: {model_name}
- **类型**: 对话生成模型
- **大小**: ~300MB
- **来源**: Microsoft/Hugging Face
- **功能**: 英文对话生成

## 使用方法
```python
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

# 加载模型
tokenizer = AutoTokenizer.from_pretrained("{model_name}")
model = AutoModelForCausalLM.from_pretrained("{model_name}")

# 生成对话
def chat(message):
    inputs = tokenizer.encode(message + tokenizer.eos_token, return_tensors="pt")
    with torch.no_grad():
        outputs = model.generate(inputs, max_length=50, temperature=0.7, do_sample=True)
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response[len(message):].strip()

# 使用示例
print(chat("Hello, how are you?"))
```

## 模型位置
模型文件已缓存到: `~/.cache/huggingface/transformers/`

## 下一步
1. 可以下载更大更强的模型
2. 集成到AI编辑器中
3. 添加更多功能
"""
    
    with open("model_info.md", "w", encoding="utf-8") as f:
        f.write(model_info)
    
    print("📄 模型信息已保存到 model_info.md")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    print("\n可能的解决方案:")
    print("1. 检查网络连接")
    print("2. 确保有足够的磁盘空间")
    print("3. 重试下载")

print("\n按任意键退出...")
input()
