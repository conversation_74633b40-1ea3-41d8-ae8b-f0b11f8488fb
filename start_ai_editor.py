#!/usr/bin/env python3
"""
本地最强AI编辑器启动脚本
"""

import os
import sys
import subprocess
import threading
import time
import webbrowser
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'torch', 'transformers', 'diffusers', 'accelerate',
        'fastapi', 'uvicorn', 'gradio', 'opencv-python'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.info("请运行以下命令安装依赖:")
        logger.info(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_models():
    """检查模型是否存在"""
    model_dir = Path("E:/ai_models")
    if not model_dir.exists() or not any(model_dir.iterdir()):
        logger.warning("未检测到AI模型，建议先下载模型")
        logger.info("运行: python download_models.py")
        return False
    return True

def start_backend():
    """启动后端服务"""
    logger.info("🚀 启动AI编辑器后端服务...")
    
    try:
        # 启动FastAPI后端
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "ai_editor_backend:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        return process
    except Exception as e:
        logger.error(f"后端启动失败: {e}")
        return None

def wait_for_backend(max_wait=30):
    """等待后端服务启动"""
    import requests
    
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:8000/", timeout=1)
            if response.status_code == 200:
                logger.info("✅ 后端服务启动成功")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 5 == 0:
            logger.info(f"等待后端启动... ({i}/{max_wait})")
    
    logger.error("❌ 后端服务启动超时")
    return False

def open_frontend():
    """打开前端界面"""
    frontend_path = Path("ai_editor_frontend.html").absolute()
    
    if not frontend_path.exists():
        logger.error("前端文件不存在")
        return False
    
    logger.info("🌐 打开前端界面...")
    webbrowser.open(f"file://{frontend_path}")
    return True

def show_system_info():
    """显示系统信息"""
    import torch
    
    print("\n" + "="*60)
    print("🤖 本地最强AI编辑器 - 系统信息")
    print("="*60)
    
    print(f"Python版本: {sys.version.split()[0]}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {'✅' if torch.cuda.is_available() else '❌'}")
    
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"GPU内存: {gpu_memory:.1f}GB")
    
    # 检查磁盘空间
    import shutil
    model_dir = Path("E:/ai_models")
    if model_dir.exists():
        total, used, free = shutil.disk_usage(model_dir)
        print(f"E盘可用空间: {free // (1024**3)}GB")
    
    # 检查已下载模型
    if model_dir.exists():
        model_count = sum(1 for _ in model_dir.rglob("*") if _.is_dir())
        print(f"已下载模型: {model_count}个")
    
    print("="*60)

def main():
    """主函数"""
    print("🚀 本地最强AI编辑器启动器")
    
    # 显示系统信息
    show_system_info()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的包")
        return
    
    # 检查模型（警告但不阻止启动）
    if not check_models():
        print("\n⚠️ 建议先下载AI模型以获得最佳体验")
        choice = input("是否继续启动？(y/n): ").lower().strip()
        if choice != 'y':
            print("请先运行: python download_models.py")
            return
    
    print("\n🎯 启动选项:")
    print("1. 完整启动（后端+前端）")
    print("2. 仅启动后端服务")
    print("3. 仅打开前端界面")
    print("4. 下载AI模型")
    print("0. 退出")
    
    choice = input("\n请选择 (0-4): ").strip()
    
    if choice == "1":
        # 完整启动
        print("\n🚀 启动完整AI编辑器...")
        
        # 启动后端
        backend_process = start_backend()
        if not backend_process:
            return
        
        try:
            # 等待后端启动
            if wait_for_backend():
                # 打开前端
                open_frontend()
                
                print("\n✅ AI编辑器启动成功！")
                print("📱 前端界面已在浏览器中打开")
                print("🔗 后端API: http://localhost:8000")
                print("📖 API文档: http://localhost:8000/docs")
                print("\n按 Ctrl+C 停止服务")
                
                # 保持运行
                backend_process.wait()
            else:
                backend_process.terminate()
                
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
            backend_process.terminate()
            backend_process.wait()
            print("👋 服务已停止")
    
    elif choice == "2":
        # 仅启动后端
        print("\n🔧 启动后端服务...")
        backend_process = start_backend()
        if backend_process:
            try:
                if wait_for_backend():
                    print("\n✅ 后端服务启动成功！")
                    print("🔗 API地址: http://localhost:8000")
                    print("📖 API文档: http://localhost:8000/docs")
                    print("\n按 Ctrl+C 停止服务")
                    backend_process.wait()
                else:
                    backend_process.terminate()
            except KeyboardInterrupt:
                print("\n🛑 正在停止服务...")
                backend_process.terminate()
                backend_process.wait()
                print("👋 服务已停止")
    
    elif choice == "3":
        # 仅打开前端
        print("\n🌐 打开前端界面...")
        if open_frontend():
            print("✅ 前端界面已打开")
            print("⚠️ 请确保后端服务正在运行 (http://localhost:8000)")
        else:
            print("❌ 前端打开失败")
    
    elif choice == "4":
        # 下载模型
        print("\n📦 启动模型下载工具...")
        subprocess.run([sys.executable, "download_models.py"])
    
    elif choice == "0":
        print("👋 再见！")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
