# 🚀 本地最强AI编辑器

一个功能完整的本地AI编辑器，支持代码生成、图像创作、视频制作、语音合成和智能对话，**完全本地运行，无任何限制**！

## ✨ 主要功能

### 💻 代码生成
- 支持多种编程语言（Python、JavaScript、Java、C++、Rust、Go）
- 基于最新的代码生成模型（DeepSeek Coder、CodeLlama）
- 智能代码补全和优化建议

### 🎨 图像生成
- 高质量图像生成（Stable Diffusion XL、FLUX.1）
- 支持多种尺寸和风格
- 负面提示词控制

### 🎬 视频生成
- 文本到视频生成
- 支持多种时长和帧率
- 基于CogVideoX等先进模型

### 🎵 语音合成
- 多语言语音合成
- 多种音色选择（男声、女声、童声）
- 情感语音生成

### 💬 智能对话
- 无限制AI助手
- 支持长对话记忆
- 专业知识问答

## 🖥️ 系统要求

### 推荐配置
- **CPU**: AMD Ryzen 5 9600X 或同等性能
- **内存**: 128GB DDR5（最低32GB）
- **GPU**: AMD Radeon RX 7900 XTX 或 NVIDIA RTX 4090
- **存储**: 500GB+ 可用空间（SSD推荐）
- **系统**: Windows 10/11, Linux, macOS

### 最低配置
- **CPU**: 4核8线程
- **内存**: 16GB
- **GPU**: 8GB VRAM（可选，CPU也能运行）
- **存储**: 100GB+ 可用空间

## 📦 安装指南

### 1. 环境准备
确保已安装Python 3.8+和必要的开发环境：

```bash
# 检查Python版本
python --version

# 安装依赖
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
pip install transformers diffusers accelerate datasets huggingface_hub
pip install fastapi uvicorn gradio streamlit opencv-python
pip install matplotlib seaborn scikit-learn pandas numpy
```

### 2. 下载项目文件
将以下文件保存到同一目录：
- `ai_editor_backend.py` - 后端服务
- `ai_editor_frontend.html` - 前端界面
- `download_models.py` - 模型下载工具
- `start_ai_editor.py` - 启动脚本

### 3. 下载AI模型
```bash
# 运行模型下载工具
python download_models.py

# 或者直接下载基础模型包
python download_models.py download
```

### 4. 启动编辑器
```bash
# 完整启动（推荐）
python start_ai_editor.py

# 选择选项1进行完整启动
```

## 🎯 快速开始

### 启动步骤
1. 运行 `python start_ai_editor.py`
2. 选择"完整启动"
3. 等待后端服务启动
4. 浏览器会自动打开前端界面
5. 开始使用各种AI功能！

### 使用示例

#### 代码生成
```
提示: 创建一个计算斐波那契数列的Python函数
语言: Python
```

#### 图像生成
```
提示: 一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影风格
尺寸: 1024x1024
```

#### 智能对话
```
用户: 请解释什么是机器学习
AI: 机器学习是人工智能的一个分支...
```

## 📁 项目结构

```
本地AI编辑器/
├── ai_editor_backend.py      # FastAPI后端服务
├── ai_editor_frontend.html   # Web前端界面
├── download_models.py        # 模型下载工具
├── start_ai_editor.py        # 启动脚本
├── README.md                 # 项目说明
├── E:/ai_models/            # AI模型存储目录
│   ├── 代码生成/
│   ├── 对话聊天/
│   ├── 图像生成/
│   ├── 视频生成/
│   └── 语音合成/
└── outputs/                 # 生成内容输出目录
```

## 🔧 配置说明

### 模型配置
- 模型存储路径: `E:/ai_models/`
- 输出文件路径: `outputs/`
- 支持CUDA和CPU运行

### 网络配置
- 后端端口: 8000
- 前端: 本地HTML文件
- API文档: http://localhost:8000/docs

## 🚀 高级功能

### 模型管理
```bash
# 查看可用模型
python download_models.py list

# 检查已下载模型
python download_models.py check

# 下载特定模型
python download_models.py
# 然后选择选项4
```

### API使用
```python
import requests

# 代码生成API
response = requests.post("http://localhost:8000/api/generate_code", json={
    "prompt": "创建一个排序算法",
    "language": "python"
})

# 图像生成API
response = requests.post("http://localhost:8000/api/generate_image", json={
    "prompt": "美丽的风景画",
    "width": 1024,
    "height": 1024
})
```

## 🛠️ 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少batch_size
   - 使用较小的模型
   - 启用CPU模式

2. **模型下载失败**
   - 检查网络连接
   - 使用镜像源
   - 手动下载模型文件

3. **后端启动失败**
   - 检查端口8000是否被占用
   - 确认依赖包已安装
   - 查看错误日志

### 性能优化

1. **GPU加速**
   - 确保CUDA正确安装
   - 使用fp16精度
   - 启用模型并行

2. **内存优化**
   - 使用模型量化
   - 启用梯度检查点
   - 清理GPU缓存

## 📊 存储需求

### 模型大小估算
- **小型配置**: ~50GB
  - 基础代码模型: 15GB
  - 对话模型: 10GB
  - 图像生成: 15GB
  - 其他: 10GB

- **中型配置**: ~200GB
  - 中等代码模型: 50GB
  - 对话模型: 30GB
  - 图像生成: 80GB
  - 视频生成: 40GB

- **大型配置**: ~500GB
  - 大型代码模型: 150GB
  - 对话模型: 100GB
  - 图像生成: 150GB
  - 视频生成: 100GB

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境
```bash
# 克隆项目
git clone <项目地址>

# 安装开发依赖
pip install -r requirements-dev.txt

# 运行测试
python -m pytest tests/
```

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🙏 致谢

感谢以下开源项目：
- [Transformers](https://github.com/huggingface/transformers)
- [Diffusers](https://github.com/huggingface/diffusers)
- [FastAPI](https://github.com/tiangolo/fastapi)
- [PyTorch](https://github.com/pytorch/pytorch)

---

**🎉 享受无限制的本地AI体验！**
