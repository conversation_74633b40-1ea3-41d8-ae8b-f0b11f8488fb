@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
title 本地AI编辑器

echo.
echo ████████████████████████████████████████
echo 🚀 本地AI编辑器 - 一键启动
echo ████████████████████████████████████████
echo.

REM 检测Python
echo 🔍 检测Python环境...
set PYTHON_FOUND=0
set PYTHON_CMD=

REM 尝试常见的Python命令
for %%P in (python python3 py) do (
    if !PYTHON_FOUND! == 0 (
        %%P --version >nul 2>&1
        if !errorlevel! == 0 (
            set PYTHON_CMD=%%P
            set PYTHON_FOUND=1
            echo ✅ 找到Python: %%P
            %%P --version
        )
    )
)

REM 尝试具体路径
if !PYTHON_FOUND! == 0 (
    echo 尝试具体Python路径...
    for %%P in ("%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe" "%USERPROFILE%\AppData\Local\Programs\Python\Python311\python.exe" "%USERPROFILE%\AppData\Local\Programs\Python\Python310\python.exe" "C:\Python312\python.exe" "C:\Python311\python.exe" "C:\Python310\python.exe") do (
        if !PYTHON_FOUND! == 0 (
            if exist %%P (
                %%P --version >nul 2>&1
                if !errorlevel! == 0 (
                    set PYTHON_CMD=%%P
                    set PYTHON_FOUND=1
                    echo ✅ 找到Python: %%P
                    %%P --version
                )
            )
        )
    )
)

if !PYTHON_FOUND! == 0 (
    echo ❌ 未找到Python！
    echo.
    echo 请先安装Python:
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装最新版本
    echo 3. 安装时勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo.
echo 🚀 启动AI编辑器...
echo 📱 浏览器将自动打开: http://localhost:8000
echo.

!PYTHON_CMD! run_ai_editor.py

if !errorlevel! neq 0 (
    echo.
    echo ❌ 启动失败，尝试其他方法...
    echo.
    pause
)

echo.
echo 👋 AI编辑器已停止
pause
