
import sys
import webbrowser
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse

class AIEditorHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == "/" or self.path == "/app":
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地AI编辑器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333; padding: 20px;
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; color: white; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .card { background: white; border-radius: 15px; padding: 30px; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .input-group { margin-bottom: 20px; }
        .input-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .input-group input, .input-group textarea, .input-group select { 
            width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 16px; 
        }
        .input-group textarea { min-height: 120px; resize: vertical; }
        .btn { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; 
            padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 16px; margin-right: 10px; 
        }
        .btn:hover { transform: translateY(-2px); }
        .output { 
            margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px; 
            border-left: 4px solid #667eea; min-height: 100px; 
        }
        .code-output { background: #2d3748; color: #e2e8f0; font-family: 'Courier New', monospace; white-space: pre-wrap; }
        .tabs { display: flex; margin-bottom: 20px; }
        .tab { flex: 1; padding: 15px; text-align: center; background: rgba(255,255,255,0.1); border: none; color: white; cursor: pointer; }
        .tab.active { background: rgba(255,255,255,0.3); }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 本地AI编辑器</h1>
            <p>代码生成 • 智能对话 • 完全本地运行</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="switchTab('code')">💻 代码生成</button>
            <button class="tab" onclick="switchTab('chat')">💬 智能对话</button>
        </div>

        <div id="code" class="tab-content active">
            <div class="card">
                <h2>💻 代码生成器</h2>
                <div class="input-group">
                    <label>编程语言</label>
                    <select id="codeLanguage">
                        <option value="python">Python</option>
                        <option value="javascript">JavaScript</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>代码需求描述</label>
                    <textarea id="codePrompt" placeholder="请描述您需要的代码功能，例如：创建一个Hello World程序"></textarea>
                </div>
                <button class="btn" onclick="generateCode()">生成代码</button>
                <div class="output code-output" id="codeOutput">生成的代码将显示在这里...</div>
            </div>
        </div>

        <div id="chat" class="tab-content">
            <div class="card">
                <h2>💬 智能对话助手</h2>
                <div class="output" id="chatOutput" style="height: 300px; overflow-y: auto;">
                    <div style="color: #666; margin-bottom: 10px;">
                        <strong>AI助手:</strong> 您好！我是您的本地AI助手。我可以帮您生成代码、回答问题。请问有什么可以帮您的吗？
                    </div>
                </div>
                <div class="input-group">
                    <input type="text" id="chatInput" placeholder="请输入您的问题..." onkeypress="if(event.key==='Enter') sendMessage()">
                </div>
                <button class="btn" onclick="sendMessage()">发送消息</button>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function generateCode() {
            const language = document.getElementById('codeLanguage').value;
            const prompt = document.getElementById('codePrompt').value;
            
            if (!prompt.trim()) {
                alert('请输入代码需求描述');
                return;
            }

            // 模拟代码生成
            const codes = {
                python: {
                    'hello world': 'print("Hello, World!")',
                    'fibonacci': `def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 测试
for i in range(10):
    print(f"fibonacci({i}) = {fibonacci(i)}")`,
                    'default': `# Python代码示例
# 根据提示: ${prompt}

def main():
    print("AI编辑器生成的代码")
    # TODO: 实现具体功能
    pass

if __name__ == "__main__":
    main()`
                },
                javascript: {
                    'hello world': 'console.log("Hello, World!");',
                    'fibonacci': `function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n-1) + fibonacci(n-2);
}

// 测试
for (let i = 0; i < 10; i++) {
    console.log(\`fibonacci(\${i}) = \${fibonacci(i)}\`);
}`,
                    'default': `// JavaScript代码示例
// 根据提示: ${prompt}

function main() {
    console.log("AI编辑器生成的代码");
    // TODO: 实现具体功能
}

main();`
                }
            };

            const langCodes = codes[language] || codes.python;
            let code = langCodes.default;
            
            for (const [key, value] of Object.entries(langCodes)) {
                if (prompt.toLowerCase().includes(key)) {
                    code = value;
                    break;
                }
            }

            document.getElementById('codeOutput').textContent = code;
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message) return;

            const chatOutput = document.getElementById('chatOutput');
            chatOutput.innerHTML += `<div style="margin-bottom: 10px;"><strong>您:</strong> ${message}</div>`;
            input.value = '';

            // 模拟AI响应
            const responses = {
                '你好': '您好！我是您的本地AI助手，很高兴为您服务！',
                'hello': 'Hello! I am your local AI assistant.',
                '代码': '我可以帮您生成Python、JavaScript、Java、C++等多种语言的代码。',
                '帮助': '我可以帮您：\n1. 生成各种编程语言的代码\n2. 回答编程相关问题\n3. 提供技术建议',
                'python': 'Python是一种简单易学的编程语言，适合初学者和专业开发者。',
                'javascript': 'JavaScript是Web开发的核心语言，可以用于前端和后端开发。'
            };

            let response = `我理解您的问题："${message}"。作为本地AI助手，我正在努力为您提供最好的帮助。`;
            
            for (const [key, value] of Object.entries(responses)) {
                if (message.toLowerCase().includes(key)) {
                    response = value;
                    break;
                }
            }

            setTimeout(() => {
                chatOutput.innerHTML += `<div style="margin-bottom: 10px; color: #666;"><strong>AI助手:</strong> ${response}</div>`;
                chatOutput.scrollTop = chatOutput.scrollHeight;
            }, 500);
        }
    </script>
</body>
</html>
            """
            self.wfile.write(html.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()

def open_browser():
    time.sleep(2)
    webbrowser.open("http://localhost:8000")

def main():
    print("🚀 启动简单AI编辑器...")
    
    # 在后台打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动HTTP服务器
    server = HTTPServer(('localhost', 8000), AIEditorHandler)
    print("🌐 服务器启动成功: http://localhost:8000")
    print("📱 浏览器将自动打开")
    print("\n按 Ctrl+C 停止服务")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        server.shutdown()

if __name__ == "__main__":
    main()
