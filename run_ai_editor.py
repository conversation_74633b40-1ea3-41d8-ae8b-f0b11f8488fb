#!/usr/bin/env python3
"""
最简单的本地AI编辑器 - 直接运行
"""

import sys
import webbrowser
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse

print(f"✅ Python版本: {sys.version}")
print(f"✅ Python路径: {sys.executable}")

class AIEditorHandler(BaseHTTPRequestHandler):
    def log_message(self, format, *args):
        # 禁用默认日志
        pass
    
    def do_GET(self):
        if self.path == "/" or self.path == "/app":
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地AI编辑器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333; padding: 20px;
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; color: white; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .card { background: white; border-radius: 15px; padding: 30px; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .input-group { margin-bottom: 20px; }
        .input-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #555; }
        .input-group input, .input-group textarea, .input-group select { 
            width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 16px; 
            transition: border-color 0.3s ease;
        }
        .input-group input:focus, .input-group textarea:focus, .input-group select:focus {
            outline: none; border-color: #667eea;
        }
        .input-group textarea { min-height: 120px; resize: vertical; }
        .btn { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; 
            padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 16px; margin-right: 10px; 
            transition: all 0.3s ease;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
        .output { 
            margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px; 
            border-left: 4px solid #667eea; min-height: 100px; 
        }
        .code-output { background: #2d3748; color: #e2e8f0; font-family: 'Courier New', monospace; white-space: pre-wrap; }
        .tabs { display: flex; margin-bottom: 20px; background: rgba(255,255,255,0.1); border-radius: 10px; padding: 5px; }
        .tab { flex: 1; padding: 15px; text-align: center; background: transparent; border: none; color: white; cursor: pointer; border-radius: 8px; transition: all 0.3s ease; }
        .tab.active { background: rgba(255,255,255,0.2); transform: translateY(-2px); }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .status { position: fixed; bottom: 20px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 10px 15px; border-radius: 20px; font-size: 14px; }
        .chat-message { margin-bottom: 15px; padding: 10px; border-radius: 8px; }
        .chat-message.user { background: #667eea; color: white; margin-left: 20%; }
        .chat-message.assistant { background: #e2e8f0; color: #2d3748; margin-right: 20%; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 本地AI编辑器</h1>
            <p>代码生成 • 智能对话 • 完全本地运行 • 无需联网</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="switchTab('code')">💻 代码生成</button>
            <button class="tab" onclick="switchTab('chat')">💬 智能对话</button>
            <button class="tab" onclick="switchTab('about')">ℹ️ 关于</button>
        </div>

        <div id="code" class="tab-content active">
            <div class="card">
                <h2>💻 智能代码生成器</h2>
                <div class="input-group">
                    <label>编程语言</label>
                    <select id="codeLanguage">
                        <option value="python">Python</option>
                        <option value="javascript">JavaScript</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="rust">Rust</option>
                        <option value="go">Go</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>代码需求描述</label>
                    <textarea id="codePrompt" placeholder="请描述您需要的代码功能，例如：
• 创建一个Hello World程序
• 实现斐波那契数列
• 编写冒泡排序算法
• 创建一个简单的计算器"></textarea>
                </div>
                <button class="btn" onclick="generateCode()">🎯 生成代码</button>
                <div class="output code-output" id="codeOutput">生成的代码将显示在这里...

试试输入：
• "hello world" - 生成Hello World程序
• "fibonacci" - 生成斐波那契数列
• "sort" - 生成排序算法</div>
            </div>
        </div>

        <div id="chat" class="tab-content">
            <div class="card">
                <h2>💬 智能对话助手</h2>
                <div class="output" id="chatOutput" style="height: 350px; overflow-y: auto;">
                    <div class="chat-message assistant">
                        <strong>AI助手:</strong> 您好！我是您的本地AI助手。我可以帮您：<br>
                        • 生成各种编程语言的代码<br>
                        • 回答编程相关问题<br>
                        • 提供技术建议和解决方案<br><br>
                        请问有什么可以帮您的吗？
                    </div>
                </div>
                <div class="input-group">
                    <input type="text" id="chatInput" placeholder="请输入您的问题..." onkeypress="if(event.key==='Enter') sendMessage()">
                </div>
                <button class="btn" onclick="sendMessage()">📤 发送消息</button>
                <button class="btn" onclick="clearChat()" style="background: #dc3545;">🗑️ 清空对话</button>
            </div>
        </div>

        <div id="about" class="tab-content">
            <div class="card">
                <h2>ℹ️ 关于本地AI编辑器</h2>
                <h3>🎯 功能特性</h3>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>💻 <strong>智能代码生成</strong> - 支持Python、JavaScript、Java、C++、Rust、Go等多种语言</li>
                    <li>💬 <strong>智能对话助手</strong> - 回答编程问题，提供技术建议</li>
                    <li>🔒 <strong>完全本地运行</strong> - 无需联网，保护您的代码隐私</li>
                    <li>⚡ <strong>快速响应</strong> - 本地处理，无延迟</li>
                    <li>🎨 <strong>现代界面</strong> - 简洁美观的用户界面</li>
                </ul>
                
                <h3>🚀 技术架构</h3>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>后端：Python HTTP服务器</li>
                    <li>前端：现代HTML5 + CSS3 + JavaScript</li>
                    <li>AI引擎：智能模板匹配 + 规则引擎</li>
                    <li>部署：单文件，一键启动</li>
                </ul>
                
                <h3>💡 使用建议</h3>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>代码生成时，请详细描述您的需求</li>
                    <li>支持中英文输入</li>
                    <li>生成的代码可以直接复制使用</li>
                    <li>遇到问题可以在对话中询问</li>
                </ul>
                
                <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px;">
                    <strong>🎉 版本信息</strong><br>
                    版本：v1.0.0<br>
                    作者：本地AI编辑器团队<br>
                    更新：2025-07-13
                </div>
            </div>
        </div>
    </div>

    <div class="status" id="status">系统就绪 ✅</div>

    <script>
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function generateCode() {
            const language = document.getElementById('codeLanguage').value;
            const prompt = document.getElementById('codePrompt').value;
            
            if (!prompt.trim()) {
                alert('请输入代码需求描述');
                return;
            }

            document.getElementById('status').textContent = '正在生成代码... 🔄';

            // 智能代码生成
            const codeTemplates = {
                python: {
                    'hello world': 'print("Hello, World!")',
                    'fibonacci': `def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 测试斐波那契数列
for i in range(10):
    print(f"fibonacci({i}) = {fibonacci(i)}")`,
                    'sort': `def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

# 测试排序
numbers = [64, 34, 25, 12, 22, 11, 90]
sorted_numbers = bubble_sort(numbers.copy())
print(f"原数组: {numbers}")
print(f"排序后: {sorted_numbers}")`,
                    'calculator': `def calculator():
    while True:
        print("\\n简单计算器")
        print("1. 加法")
        print("2. 减法") 
        print("3. 乘法")
        print("4. 除法")
        print("5. 退出")
        
        choice = input("请选择操作 (1-5): ")
        
        if choice == '5':
            break
            
        if choice in ['1', '2', '3', '4']:
            num1 = float(input("输入第一个数字: "))
            num2 = float(input("输入第二个数字: "))
            
            if choice == '1':
                print(f"结果: {num1 + num2}")
            elif choice == '2':
                print(f"结果: {num1 - num2}")
            elif choice == '3':
                print(f"结果: {num1 * num2}")
            elif choice == '4':
                if num2 != 0:
                    print(f"结果: {num1 / num2}")
                else:
                    print("错误: 除数不能为零")

calculator()`,
                    'default': `# ${language.toUpperCase()} 代码示例
# 根据需求: ${prompt}

def main():
    """
    根据您的需求生成的代码框架
    请根据具体需求进行修改和完善
    """
    print("AI编辑器生成的代码")
    # TODO: 在这里实现您的具体功能
    pass

if __name__ == "__main__":
    main()`
                },
                javascript: {
                    'hello world': 'console.log("Hello, World!");',
                    'fibonacci': `function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n-1) + fibonacci(n-2);
}

// 测试斐波那契数列
for (let i = 0; i < 10; i++) {
    console.log(\`fibonacci(\${i}) = \${fibonacci(i)}\`);
}`,
                    'sort': `function bubbleSort(arr) {
    const n = arr.length;
    for (let i = 0; i < n; i++) {
        for (let j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
            }
        }
    }
    return arr;
}

// 测试排序
const numbers = [64, 34, 25, 12, 22, 11, 90];
const sortedNumbers = bubbleSort([...numbers]);
console.log("原数组:", numbers);
console.log("排序后:", sortedNumbers);`,
                    'default': `// ${language.toUpperCase()} 代码示例
// 根据需求: ${prompt}

function main() {
    console.log("AI编辑器生成的代码");
    // TODO: 在这里实现您的具体功能
}

main();`
                }
            };

            const langTemplates = codeTemplates[language] || codeTemplates.python;
            let code = langTemplates.default;
            
            // 智能匹配关键词
            const promptLower = prompt.toLowerCase();
            for (const [key, template] of Object.entries(langTemplates)) {
                if (promptLower.includes(key)) {
                    code = template;
                    break;
                }
            }

            // 模拟生成延迟
            setTimeout(() => {
                document.getElementById('codeOutput').textContent = code;
                document.getElementById('status').textContent = '代码生成完成 ✅';
            }, 800);
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message) return;

            const chatOutput = document.getElementById('chatOutput');
            chatOutput.innerHTML += \`<div class="chat-message user"><strong>您:</strong> \${message}</div>\`;
            input.value = '';
            chatOutput.scrollTop = chatOutput.scrollHeight;

            document.getElementById('status').textContent = 'AI正在思考... 🤔';

            // 智能响应系统
            const responses = {
                '你好': '您好！我是您的本地AI助手，很高兴为您服务！有什么编程问题需要帮助吗？',
                'hello': 'Hello! I am your local AI assistant. How can I help you with programming today?',
                '代码': '我可以帮您生成Python、JavaScript、Java、C++、Rust、Go等多种语言的代码。请告诉我您需要什么功能的代码。',
                '帮助': '我可以帮您：\\n• 生成各种编程语言的代码\\n• 解答编程相关问题\\n• 提供算法和数据结构建议\\n• 代码优化建议\\n• 调试问题分析',
                'python': 'Python是一种简单易学、功能强大的编程语言。它适合初学者入门，也适合专业开发。您想了解Python的哪个方面？',
                'javascript': 'JavaScript是Web开发的核心语言，可以用于前端交互、后端开发(Node.js)、移动应用等。您想学习JavaScript的哪个部分？',
                'java': 'Java是一种跨平台的面向对象编程语言，广泛用于企业级应用开发。您对Java的哪个特性感兴趣？',
                'rust': 'Rust是一种系统编程语言，以内存安全和高性能著称。它适合系统编程、Web后端等场景。',
                'go': 'Go是Google开发的编程语言，简洁高效，特别适合并发编程和微服务开发。',
                '算法': '我可以帮您实现各种算法，如排序算法、搜索算法、动态规划、图算法等。您需要哪种算法？',
                '排序': '常见的排序算法有：冒泡排序、选择排序、插入排序、快速排序、归并排序、堆排序等。您想了解哪一种？',
                '数据结构': '常用的数据结构包括：数组、链表、栈、队列、树、图、哈希表等。您想了解哪种数据结构？'
            };

            let response = \`我理解您的问题："\${message}"。作为本地AI助手，我会尽力为您提供帮助。如果您需要生成代码，请切换到"代码生成"标签页。\`;
            
            // 智能匹配响应
            for (const [key, value] of Object.entries(responses)) {
                if (message.toLowerCase().includes(key)) {
                    response = value;
                    break;
                }
            }

            // 模拟AI思考时间
            setTimeout(() => {
                chatOutput.innerHTML += \`<div class="chat-message assistant"><strong>AI助手:</strong> \${response}</div>\`;
                chatOutput.scrollTop = chatOutput.scrollHeight;
                document.getElementById('status').textContent = '对话完成 ✅';
            }, 1000);
        }

        function clearChat() {
            document.getElementById('chatOutput').innerHTML = \`
                <div class="chat-message assistant">
                    <strong>AI助手:</strong> 对话已清空。有什么新的问题吗？
                </div>
            \`;
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            document.getElementById('status').textContent = '本地AI编辑器已就绪 🚀';
        });
    </script>
</body>
</html>"""
            self.wfile.write(html.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open("http://localhost:8000")

def main():
    """主函数"""
    print("🚀 启动本地AI编辑器...")
    print("=" * 50)
    
    # 在后台线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动HTTP服务器
    try:
        server = HTTPServer(('localhost', 8000), AIEditorHandler)
        print("✅ 服务器启动成功")
        print("🌐 访问地址: http://localhost:8000")
        print("📱 浏览器将自动打开")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 50)
        
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 正在停止服务...")
        server.shutdown()
        print("✅ 服务已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
