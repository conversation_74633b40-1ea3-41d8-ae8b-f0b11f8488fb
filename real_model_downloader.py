#!/usr/bin/env python3
"""
真正的Hugging Face模型下载器
从 https://huggingface.co/ 下载实际的大模型
"""

import os
import sys
from pathlib import Path
import logging
from huggingface_hub import snapshot_download, login, whoami
import torch
import psutil
import shutil

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 模型存储目录 - 使用E盘大容量存储
MODEL_DIR = Path("E:/huggingface_models")
MODEL_DIR.mkdir(exist_ok=True)

# 推荐的真实模型列表 (从小到大)
REAL_MODELS = {
    "代码生成": {
        "small": {
            "name": "microsoft/CodeBERT-base",
            "size": "~500MB",
            "description": "微软CodeBERT，代码理解模型"
        },
        "medium": {
            "name": "Salesforce/codet5-base",
            "size": "~1GB", 
            "description": "Salesforce CodeT5，代码生成模型"
        },
        "large": {
            "name": "microsoft/DialoGPT-large",
            "size": "~3GB",
            "description": "微软大型对话模型，可用于代码对话"
        },
        "xlarge": {
            "name": "bigcode/starcoder2-3b",
            "size": "~6GB",
            "description": "StarCoder2 3B，专业代码生成模型"
        }
    },
    "对话聊天": {
        "small": {
            "name": "microsoft/DialoGPT-small",
            "size": "~300MB",
            "description": "微软小型对话模型"
        },
        "medium": {
            "name": "microsoft/DialoGPT-medium", 
            "size": "~800MB",
            "description": "微软中型对话模型"
        },
        "large": {
            "name": "microsoft/DialoGPT-large",
            "size": "~3GB",
            "description": "微软大型对话模型"
        },
        "xlarge": {
            "name": "microsoft/GODEL-v1_1-large-seq2seq",
            "size": "~5GB",
            "description": "微软GODEL大型对话模型"
        }
    },
    "文本生成": {
        "small": {
            "name": "gpt2",
            "size": "~500MB", 
            "description": "OpenAI GPT-2，经典文本生成模型"
        },
        "medium": {
            "name": "gpt2-medium",
            "size": "~1.5GB",
            "description": "GPT-2 Medium版本"
        },
        "large": {
            "name": "gpt2-large", 
            "size": "~3GB",
            "description": "GPT-2 Large版本"
        },
        "xlarge": {
            "name": "EleutherAI/gpt-neo-1.3B",
            "size": "~5GB",
            "description": "EleutherAI GPT-Neo 1.3B"
        }
    },
    "中文模型": {
        "small": {
            "name": "uer/gpt2-chinese-cluecorpussmall",
            "size": "~500MB",
            "description": "中文GPT-2小模型"
        },
        "medium": {
            "name": "uer/gpt2-chinese-poem",
            "size": "~800MB", 
            "description": "中文诗歌生成模型"
        },
        "large": {
            "name": "THUDM/chatglm-6b",
            "size": "~12GB",
            "description": "清华ChatGLM-6B中文对话模型"
        }
    }
}

def check_system_info():
    """检查系统信息"""
    print("🖥️ 系统信息检查:")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    # 检查内存
    memory = psutil.virtual_memory()
    print(f"系统内存: {memory.total / 1024**3:.1f}GB (可用: {memory.available / 1024**3:.1f}GB)")
    
    # 检查磁盘空间
    total, used, free = shutil.disk_usage(MODEL_DIR)
    print(f"E盘空间: {free / 1024**3:.1f}GB 可用")
    print(f"模型存储目录: {MODEL_DIR}")
    print()

def list_available_models():
    """列出可用模型"""
    print("🤖 可下载的真实AI模型:")
    print("=" * 80)
    
    for category, models in REAL_MODELS.items():
        print(f"\n📂 {category}:")
        for size, info in models.items():
            print(f"  {size:8} - {info['name']}")
            print(f"           大小: {info['size']}, 描述: {info['description']}")

def download_model(model_name, category, size):
    """下载单个模型"""
    try:
        logger.info(f"🚀 开始下载模型: {model_name}")
        logger.info(f"类别: {category}, 大小: {size}")
        
        # 创建模型特定目录
        model_path = MODEL_DIR / category / size / model_name.replace("/", "_")
        model_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"下载到: {model_path}")
        
        # 下载模型
        snapshot_download(
            repo_id=model_name,
            cache_dir=str(model_path),
            resume_download=True,
            local_files_only=False
        )
        
        logger.info(f"✅ {model_name} 下载完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ {model_name} 下载失败: {e}")
        return False

def download_recommended_models():
    """下载推荐的模型组合"""
    logger.info("🎯 开始下载推荐模型组合...")
    
    # 检查可用空间
    total, used, free = shutil.disk_usage(MODEL_DIR)
    free_gb = free / 1024**3
    
    if free_gb < 10:
        logger.error("❌ 磁盘空间不足10GB，无法下载模型")
        return
    
    # 根据可用空间选择模型
    if free_gb > 50:
        size_preference = "large"
        logger.info("💪 检测到充足空间，下载大模型")
    elif free_gb > 20:
        size_preference = "medium" 
        logger.info("📦 空间适中，下载中等模型")
    else:
        size_preference = "small"
        logger.info("💾 空间有限，下载小模型")
    
    # 推荐下载的模型
    recommended = [
        ("对话聊天", size_preference),
        ("代码生成", size_preference),
        ("文本生成", "small"),  # 总是下载小的文本生成模型
    ]
    
    success_count = 0
    total_count = len(recommended)
    
    for category, size in recommended:
        if category in REAL_MODELS and size in REAL_MODELS[category]:
            model_info = REAL_MODELS[category][size]
            if download_model(model_info["name"], category, size):
                success_count += 1
    
    logger.info(f"📊 下载完成: {success_count}/{total_count} 个模型成功")
    return success_count, total_count

def test_downloaded_model():
    """测试已下载的模型"""
    logger.info("🧪 测试已下载的模型...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
        
        # 尝试加载一个小模型进行测试
        model_name = "microsoft/DialoGPT-small"
        
        logger.info(f"加载模型: {model_name}")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(model_name)
        
        # 简单测试
        test_input = "Hello, how are you?"
        inputs = tokenizer.encode(test_input + tokenizer.eos_token, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(inputs, max_length=50, temperature=0.7, do_sample=True)
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        logger.info(f"✅ 模型测试成功!")
        logger.info(f"输入: {test_input}")
        logger.info(f"输出: {response}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 模型测试失败: {e}")
        return False

def check_downloaded_models():
    """检查已下载的模型"""
    print("📦 已下载的模型:")
    print("=" * 80)
    
    if not MODEL_DIR.exists():
        print("❌ 模型目录不存在")
        return
    
    total_size = 0
    model_count = 0
    
    for category_dir in MODEL_DIR.iterdir():
        if category_dir.is_dir():
            print(f"\n📂 {category_dir.name}:")
            for size_dir in category_dir.iterdir():
                if size_dir.is_dir():
                    for model_dir in size_dir.iterdir():
                        if model_dir.is_dir():
                            # 计算模型大小
                            size_bytes = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file())
                            size_mb = size_bytes / (1024*1024)
                            total_size += size_mb
                            model_count += 1
                            
                            print(f"  ✅ {size_dir.name} - {model_dir.name} ({size_mb:.1f}MB)")
    
    print(f"\n📊 总计: {model_count} 个模型, {total_size:.1f}MB ({total_size/1024:.1f}GB)")

def main():
    """主函数"""
    print("🚀 Hugging Face 真实模型下载器")
    print("=" * 80)
    
    # 检查系统信息
    check_system_info()
    
    if len(sys.argv) == 1:
        # 交互式菜单
        while True:
            print("\n请选择操作:")
            print("1. 下载推荐模型组合")
            print("2. 列出可用模型")
            print("3. 检查已下载模型")
            print("4. 测试模型")
            print("5. 下载特定模型")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-5): ").strip()
            
            if choice == "1":
                download_recommended_models()
            elif choice == "2":
                list_available_models()
            elif choice == "3":
                check_downloaded_models()
            elif choice == "4":
                test_downloaded_model()
            elif choice == "5":
                list_available_models()
                category = input("\n请输入模型类别: ").strip()
                size = input("请输入模型大小 (small/medium/large/xlarge): ").strip()
                if category in REAL_MODELS and size in REAL_MODELS[category]:
                    model_info = REAL_MODELS[category][size]
                    download_model(model_info["name"], category, size)
                else:
                    print("❌ 无效的类别或大小")
            elif choice == "0":
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重试")
    
    elif len(sys.argv) == 2:
        if sys.argv[1] == "download":
            download_recommended_models()
        elif sys.argv[1] == "list":
            list_available_models()
        elif sys.argv[1] == "check":
            check_downloaded_models()
        elif sys.argv[1] == "test":
            test_downloaded_model()
        else:
            print("用法: python real_model_downloader.py [download|list|check|test]")

if __name__ == "__main__":
    main()
